# AGENT.md - Breath2 iOS App

## Build/Test Commands
- **Build**: `xcodebuild -scheme Breath2 -destination 'platform=iOS Simulator,name=iPhone 15' build`
- **Test All**: `xcodebuild -scheme Breath2 -destination 'platform=iOS Simulator,name=iPhone 15' test`
- **Test Single**: `xcodebuild -scheme Breath2 -destination 'platform=iOS Simulator,name=iPhone 15' test -only-testing:Breath2Tests/PitchDetectionTests/testSpecificMethod`
- **Clean**: `xcodebuild -scheme Breath2 clean`

## Architecture
- **Native iOS**: SwiftUI + AVFoundation + Accelerate framework
- **Main Project**: `Breath2/Breath2.xcodeproj` with targets: Breath2, Breath2Tests, Breath2UITests
- **Core Components**: PitchDetector, AudioManager, BreathDetector, PressureCalculator, TherapySession
- **Configuration**: AlgorithmConfiguration with presets and 50+ tunable parameters
- **Data**: UserDefaults + SessionHistoryManager for persistence

## Code Style
- **Naming**: PascalCase for types, camelCase for variables/functions
- **Imports**: Foundation first, then AVFoundation, then SwiftUI, then local imports
- **Comments**: Brief header comments with author/date, inline comments for complex algorithms
- **Types**: Use explicit types for configuration parameters, infer for simple assignments
- **Error Handling**: Comprehensive error handling with proper throw/catch patterns
- **MARK**: Use `// MARK: - Section` for code organization, especially in large classes
- **Architecture**: Clean separation between audio processing, UI, and data management layers
