# Product Requirements Document (PRD)
# Sada - PEP Therapy iOS Application

## Table of Contents
1. [Executive Summary](#executive-summary)
2. [Product Overview](#product-overview)
3. [Target Users](#target-users)
4. [Core Features](#core-features)
5. [Technical Architecture](#technical-architecture)
6. [User Interface Requirements](#user-interface-requirements)
7. [iPad-Specific Requirements](#ipad-specific-requirements)
8. [Audio Processing Requirements](#audio-processing-requirements)
9. [Data Management](#data-management)
10. [Configuration System](#configuration-system)
11. [Quality Assurance](#quality-assurance)
12. [Performance Requirements](#performance-requirements)
13. [Security & Privacy](#security--privacy)
14. [Development Guidelines](#development-guidelines)
15. [Testing Strategy](#testing-strategy)
16. [Deployment Requirements](#deployment-requirements)

---

## Executive Summary

Sada is a native iOS application that provides Positive Expiratory Pressure (PEP) therapy guidance using advanced audio signal processing. The app converts microphone input into real-time pressure measurements, guides users through structured breathing exercises, and tracks therapy progress over time.

### Key Value Propositions
- **Real-time Audio-to-Pressure Conversion**: Uses research-backed algorithms to convert breathing sounds into pressure measurements
- **Structured Therapy Sessions**: Configurable daily therapy blocks with step-by-step guidance
- **Progress Tracking**: Comprehensive session history with quality metrics and adherence monitoring
- **Clinical Accuracy**: Based on published research with r² = 0.886 correlation between audio frequency and pressure

---

## Product Overview

### What is PEP Therapy?
Positive Expiratory Pressure (PEP) therapy is a respiratory treatment technique where patients breathe out against resistance to help clear mucus from the lungs and improve breathing. The target pressure range is 10-20 cm H₂O.

### How Sada Works
1. **Audio Capture**: Records breathing sounds through the device microphone
2. **Signal Processing**: Applies advanced pitch detection algorithms to extract frequency data
3. **Pressure Calculation**: Converts detected frequencies to pressure measurements using a linear model (Pressure = -4.659 + 1.119 × Frequency)
4. **Real-time Feedback**: Provides immediate visual and textual guidance to maintain target pressure
5. **Session Management**: Tracks individual breaths, session progress, and long-term adherence

---

## Target Users

### Primary Users
- **Patients with Respiratory Conditions**: Individuals prescribed PEP therapy (COPD, cystic fibrosis, bronchiectasis)
- **Healthcare Providers**: Clinicians monitoring patient adherence and progress
- **Caregivers**: Family members assisting with therapy sessions

### User Personas
1. **Adult Patient (35-65)**: Diagnosed with COPD, needs daily therapy, moderate tech literacy
2. **Pediatric Patient (8-17)**: Cystic fibrosis patient, high tech literacy, needs engaging interface
3. **Elderly Patient (65+)**: Limited tech experience, needs simple, clear interface
4. **Healthcare Provider**: Needs detailed analytics and adherence data

---

## Core Features

### 1. Real-Time Therapy Sessions
**Purpose**: Guide users through structured breathing exercises with live feedback

**Key Components**:
- Circular progress indicator showing current pressure vs. target range
- Color-coded zones (Green: 10-20 cm H₂O, Amber: <10 or >20, Red: >25)
- Real-time action messages ("Breathe", "Perfect!", "Harder", "Softer")
- Step-by-step progress tracking
- Breath detection and validation (minimum 1.5 seconds)

**User Flow**:
1. User opens app and sees welcome screen with today's progress
2. Taps "Start Session" button
3. App requests microphone permission (if not granted)
4. Session begins with real-time pressure feedback
5. User completes required number of exhalations (typically 10-15)
6. Session ends with quality summary and next steps

### 2. Session Configuration
**Purpose**: Allow customization of therapy routines based on medical prescriptions

**Configuration Options**:
- **Daily Blocks**: 1-3 sessions per day
- **Exhalations per Block**: 5-15 breaths
- **Session Names**: Morning, Afternoon, Evening
- **Preset Configurations**: Single session, Two sessions, Three sessions

**Validation Rules**:
- Maximum 30 exhalations per session
- Maximum 3 sessions per day
- Minimum 5 exhalations per block
- Maximum 15 exhalations per block

### 3. Progress Tracking & History
**Purpose**: Simple, at-a-glance progress monitoring with minimal cognitive load

**Design Principles**:
- **Minimal Information**: Show only essential metrics that matter to users
- **Instant Understanding**: All information should be graspable within 2-3 seconds
- **Visual Clarity**: Use progressive disclosure - start simple, drill down if needed
- **Logical Flow**: Information hierarchy that matches user mental models

**Core Metrics (Simplified)**:
1. **Today's Progress**: Simple completion indicator (e.g., "2 of 3 sessions completed")
2. **Weekly Streak**: Visual streak counter (e.g., "5 days in a row")
3. **Session Quality**: Single, color-coded quality indicator per session
4. **Consistency Score**: One unified metric (0-100) representing overall performance

**History Interface**:
- **Clean Session List**: Minimal cards showing date, duration, and quality icon
- **Simple Time Navigation**: Today, This Week, This Month (no overwhelming filters)
- **One-Tap Session Details**: Progressive disclosure of detailed metrics only when needed

### 4. Analytics Dashboard (Simplified)
**Purpose**: Essential insights without overwhelming complexity

**Key Visual Elements**:
- **Weekly Progress Ring**: Single circular progress indicator showing completion rate
- **Quality Trend**: Simple line graph showing improvement over time
- **Streak Counter**: Visual representation of consecutive successful days
- **Achievement Badges**: Simple milestone recognition (1 week, 1 month, etc.)

**Simplified Metrics**:
- **Adherence Rate**: Percentage of planned sessions completed (weekly/monthly)
- **Quality Score**: Average session quality on 1-5 scale
- **Consistency**: How regularly sessions are completed
- **Time in Target**: Simple percentage of breaths in optimal zone

**Information Architecture**:
```
History Tab Structure:
├── Today's Summary (prominent)
├── This Week Progress (visual ring)
├── Recent Sessions (3-5 most recent)
└── View More (progressive disclosure)
    ├── Monthly Overview
    ├── Achievement History
    └── Detailed Session Data
```

---

## Technical Architecture

### Platform Requirements
- **Target Platform**: iOS 15.0+ (iPhone and iPad)
- **Device Support**: iPhone 8+ and iPad (6th generation)+
- **Development Language**: Swift 5.7+
- **UI Framework**: SwiftUI with adaptive layouts
- **Audio Framework**: AVFoundation
- **Signal Processing**: Accelerate framework
- **Architecture Pattern**: MVVM with ObservableObject

### Core Components

#### 1. Audio Processing Pipeline
```
Microphone Input → AudioManager → PitchDetector → PressureCalculator → UI Updates
```

**AudioManager**:
- Manages AVAudioEngine and microphone permissions
- Processes audio buffers at 44.1kHz sample rate
- Handles audio session configuration
- Provides real-time audio level monitoring

**PitchDetector**:
- Implements autocorrelation-based pitch detection
- Processes audio in 0.1-second chunks
- Applies signal filtering and downsampling
- Detects frequencies in 7-40 Hz range
- Uses economical search algorithms for performance

**PressureCalculator**:
- Converts frequency to pressure using linear model
- Validates frequency range (7-40 Hz)
- Clamps pressure output (0-30 cm H₂O)
- Provides confidence scoring

#### 2. Breath Detection System
**BreathDetector State Machine**:
```swift
enum BreathState {
    case idle
    case starting
    case active
    case ending
    case completed
}

class ImprovedBreathDetector {
    // Configuration parameters
    let startThresholdDb: Float = -40.0        // dB threshold to start detection
    let stopThresholdDb: Float = -50.0         // dB threshold to stop detection
    let minActiveFrames: Int = 15              // minimum frames in active state
    let graceFrames: Int = 5                   // grace period for noise spikes
    let minInterBreathMs: Double = 800.0       // minimum time between breaths
    let minBreathDurationMs: Double = 1500.0   // minimum breath duration
    let maxBreathDurationMs: Double = 15000.0  // maximum breath duration
    
    // Smoothing parameters
    let smoothingWindow: Int = 5               // moving average window
    let hysteresis: Float = 3.0                // dB hysteresis for state changes
    
    // State tracking
    private var currentState: BreathState = .idle
    private var breathStartTime: Date?
    private var breathEndTime: Date?
    private var activeFrameCount: Int = 0
    private var graceFrameCount: Int = 0
    private var audioLevelHistory: [Float] = []
    private var lastBreathTime: Date?
}
```

**State Transitions**:
1. **idle → starting**: Audio level > startThresholdDb for 3 consecutive frames
2. **starting → active**: activeFrameCount > minActiveFrames
3. **active → ending**: Audio level < stopThresholdDb for graceFrames
4. **ending → completed**: Duration > minBreathDurationMs
5. **ending → idle**: Duration < minBreathDurationMs (invalid breath)

**Breath Validation**:
- Minimum duration: 1.5 seconds
- Maximum duration: 15 seconds
- Minimum inter-breath interval: 800ms
- Signal strength: >-40dB for start, <-50dB for stop
- Noise rejection: 5-frame grace period with 3dB hysteresis

#### 3. Session Management
**TherapySession**:
- Manages active therapy sessions
- Tracks step progression and performance
- Calculates session quality in real-time
- Handles session timing and duration

**TherapyConfiguration**:
- Stores daily therapy blocks
- Manages session presets
- Handles daily progress reset
- Validates configuration parameters

#### 4. Data Persistence
**SessionHistoryManager**:
- File-based storage system
- Automatic data archiving (30+ days)
- Migration from UserDefaults
- Cleanup of old data (1+ year)

### Research-Validated Algorithm
**Core Algorithm**:
- Based on published research with r² = 0.886 correlation
- Linear conversion model: Pressure = -4.659 + 1.119 × Frequency (Hz)
- Frequency detection range: 7-40 Hz
- Validated across 9,993 measurements

---

## User Interface Requirements

### Design Principles
- **Medical Device Aesthetic**: Professional, clean, trustworthy
- **Dark Theme**: Reduces eye strain during therapy
- **High Contrast**: Clear visibility for all age groups
- **Responsive Design**: Adapts to different iPhone, iPad screen sizes
- **Accessibility**: VoiceOver support, large text compatibility

### Color Scheme
Based on the SADA logo aesthetic, the app uses a sophisticated medical-grade color palette:

#### Primary Colors
- **Deep Navy Blue**: #2C5F7C (Primary brand color from "sa" in logo)
- **Teal Blue**: #4A8FA8 (Secondary brand color)
- **Sage Green**: #7FB069 (Accent color from "da" in logo)
- **Mint Green**: #9BC995 (Light accent for highlights)

#### Background Colors
- **Primary Background**: Deep navy gradient (RGB: 44,95,124 to 37,75,95)
- **Secondary Background**: Teal blue with 10% opacity (#4A8FA8 at 0.1 alpha)
- **Card Backgrounds**: Navy blue with 15% opacity (#2C5F7C at 0.15 alpha)
- **Surface Colors**: White with 5-8% opacity for subtle elevation

#### Functional Colors
- **Success/Target Zone**: Sage Green (#7FB069)
- **Good Performance**: Mint Green (#9BC995)
- **Warning/Caution**: Warm amber (#D4A574) - complementary to the blue palette
- **Error/Danger**: Muted coral (#C67B5C) - softer than pure red, medical-appropriate
- **Info/Neutral**: Light teal (#6BA3B8)

#### Text Colors
- **Primary Text**: White (#FFFFFF)
- **Secondary Text**: White with 80% opacity (#FFFFFF at 0.8 alpha)
- **Tertiary Text**: White with 60% opacity (#FFFFFF at 0.6 alpha)
- **Disabled Text**: White with 40% opacity (#FFFFFF at 0.4 alpha)

#### Pressure Zone Colors
- **Green Zone (10-20 cm H₂O)**: Sage Green (#7FB069)
- **Amber Zone (<10 or >20 cm H₂O)**: Warm amber (#D4A574)
- **Red Zone (>25 cm H₂O)**: Muted coral (#C67B5C)

#### Gradient Definitions
```swift
// Primary background gradient
LinearGradient(
    colors: [
        Color(red: 44/255, green: 95/255, blue: 124/255),    // Deep Navy
        Color(red: 37/255, green: 75/255, blue: 95/255),     // Darker Navy
        Color(red: 30/255, green: 60/255, blue: 80/255)      // Deepest Navy
    ],
    startPoint: .top,
    endPoint: .bottom
)

// Accent gradient for buttons and highlights
LinearGradient(
    colors: [
        Color(red: 127/255, green: 176/255, blue: 105/255),  // Sage Green
        Color(red: 155/255, green: 201/255, blue: 149/255)   // Mint Green
    ],
    startPoint: .leading,
    endPoint: .trailing
)
```

#### Accessibility Considerations
- **High Contrast Mode**: All colors meet WCAG AA standards (4.5:1 contrast ratio minimum)
- **Color Blindness**: Information never relies solely on color; always paired with icons or text
- **Dark Mode Optimization**: Colors specifically chosen for reduced eye strain in low-light conditions
- **Medical Environment**: Calming, professional palette suitable for healthcare settings

### Navigation Structure
**Tab Bar Navigation** (4 tabs):
1. **History** (chart.line.uptrend.xyaxis): Session history and analytics
2. **PEP Therapy** (lungs.fill): Main therapy interface [Default]
3. **Settings** (gearshape.fill): Configuration and preferences
4. **Profile** (person.fill): User profile and information

### Screen Specifications

#### Welcome Screen
- **Header**: Animated breathing icon with app title
- **Progress Summary**: Today's completion status
- **Session Blocks**: List of planned sessions with status indicators
- **Start Button**: Prominent call-to-action
- **Permission Status**: Microphone access indicator

#### Therapy Session Screen
- **Header**: App logo and current session name
- **Progress Steps**: Visual step indicator with performance colors
- **Main Circle**: Large circular progress view (180-260px diameter)
- **Pressure Display**: Real-time pressure reading
- **Action Message**: Prominent feedback text
- **Control Buttons**: Pause, Stop, Next Step actions
- **Session Timer**: Duration display

#### History Screen (Simplified & Intuitive)
**Top Section (Always Visible)**:
- **Today's Progress**: Large, clear completion indicator (e.g., "2 of 3 sessions ✓")
- **Weekly Progress Ring**: Single circular indicator showing weekly completion rate
- **Current Streak**: Visual counter (e.g., "5 days in a row 🔥")

**Middle Section (Recent Activity)**:
- **Recent Sessions List**: Clean, minimal session cards showing:
  - Date/time
  - Duration (e.g., "3 min")
  - Quality icon (single color-coded circle: green/amber/red)
  - Single tap to view details

**Bottom Section (Progressive Disclosure)**:
- **"View More" Button**: Reveals additional analytics only when needed
- **Simple Time Navigation**: Today / This Week / This Month (no overwhelming filters)
- **Achievement Badges**: Simple milestone recognition

**Session Detail View (Simplified)**:
- **Session Summary**: Duration, quality, completion status
- **Simple Pressure Chart**: One visual showing time in target zone
- **Key Insight**: Single actionable takeaway (e.g., "Great consistency!")
- **No complex multi-tab analytics**

#### Settings Screen
- **Therapy Configuration**: Session setup and presets
- **Developer Settings**: Algorithm tuning (DEBUG builds only)
- **About**: App information and version

### Responsive Design Guidelines
- **iPhone SE (375pt width)**: Compact layout, smaller circles, reduced padding
- **Standard iPhones (390-428pt)**: Default layout specifications
- **iPhone Pro Max (428pt+)**: Expanded layout with larger elements
- **iPad (768pt+ width)**: Enhanced layouts with multi-column designs
- **Dynamic spacing**: Adaptive padding and margins based on screen size

### History Interface Design Principles

#### Visual Hierarchy (Simplified)
```
Priority 1 (Most Prominent):
├── Today's completion status
├── Current streak counter
└── Weekly progress ring

Priority 2 (Secondary):
├── Recent session cards (3-5 most recent)
└── Quality trend indicator

Priority 3 (Tertiary - Progressive Disclosure):
├── Monthly overview
├── Achievement badges
└── Detailed analytics
```

#### Information Design Rules
- **2-Second Rule**: All primary information must be understandable within 2 seconds
- **Single Glance**: Key metrics visible without scrolling
- **Color Coding**: Consistent color language (green = good, amber = needs attention, red = poor)
- **Progressive Disclosure**: Advanced metrics hidden behind "View More"
- **Minimal Cognitive Load**: Max 3-4 pieces of information per screen section

#### Session Card Design (Minimal)
```swift
struct SimpleSessionCard: View {
    var body: some View {
        HStack {
            QualityIndicator()      // Single colored circle
            VStack(alignment: .leading) {
                Text("Today, 2:30 PM")  // Date/time
                Text("3 min")           // Duration
            }
            Spacer()
            ChevronRight()          // Indicates tap for details
        }
        .padding()
        .background(Color.cardBackground)
        .cornerRadius(12)
    }
}
```

#### Interaction Patterns
- **Single Tap**: View session details
- **No Swipe Gestures**: Keep interactions simple
- **Large Touch Targets**: Minimum 44pt for all interactive elements
- **Instant Feedback**: Immediate visual response to taps

---

## iPad-Specific Requirements

### Design Philosophy for iPad
The iPad version of Sada leverages the larger screen real estate to provide an enhanced therapy experience while maintaining the core medical device aesthetic and functionality. The design adapts to both portrait and landscape orientations with optimized layouts for each.

### iPad Screen Size Classes
- **iPad (9.7", 10.2", 10.9")**: 768×1024pt (portrait), 1024×768pt (landscape)
- **iPad Air (10.9")**: 820×1180pt (portrait), 1180×820pt (landscape)
- **iPad Pro (11")**: 834×1194pt (portrait), 1194×834pt (landscape)
- **iPad Pro (12.9")**: 1024×1366pt (portrait), 1366×1024pt (landscape)

### Navigation Structure for iPad

#### Portrait Orientation
- **Tab Bar Navigation**: Maintains familiar 4-tab structure from iPhone
- **Enhanced Tab Bar**: Larger icons and text labels for better touch targets
- **Tab Bar Position**: Bottom of screen (consistent with iPhone)

#### Landscape Orientation
- **Sidebar Navigation**: Transform tab bar into collapsible sidebar on left
- **Split View Support**: Main content area with navigation sidebar
- **Adaptive Layout**: Sidebar can be hidden/shown based on content needs

### iPad-Specific Layout Adaptations

#### Welcome Screen (iPad)
**Portrait Mode**:
- **Two-Column Layout**: Progress summary and session blocks side-by-side
- **Larger Header**: Expanded breathing animation (150px diameter)
- **Enhanced Cards**: Wider session block cards with more detailed information
- **Prominent Start Button**: Full-width button with larger touch target

**Landscape Mode**:
- **Three-Column Layout**: Header, progress summary, and session blocks
- **Horizontal Flow**: Session blocks arranged horizontally
- **Side-by-Side Content**: Today's progress and completed sessions in columns

#### Therapy Session Screen (iPad)
**Portrait Mode**:
- **Centered Layout**: Main circular progress view (400-500px diameter)
- **Side Panels**: Step progress and controls in dedicated side areas
- **Enhanced Feedback**: Larger action messages and pressure displays
- **Floating Controls**: Control buttons in floating panels

**Landscape Mode**:
- **Split Layout**: Circular progress on left, session info and controls on right
- **Dashboard View**: Multiple information panels arranged efficiently
- **Real-time Charts**: Additional space for pressure trend graphs
- **Enhanced Metrics**: More detailed real-time statistics

#### History Screen (iPad)
**Portrait Mode**:
- **Master-Detail**: Session list on left, detailed view on right (when selected)
- **Enhanced Cards**: Larger session cards with more information
- **Expanded Charts**: Bigger adherence charts and trend visualizations
- **Filter Sidebar**: Time filters in dedicated sidebar

**Landscape Mode**:
- **Three-Pane Layout**: Filters, session list, and detail view
- **Dashboard Analytics**: Multiple charts and metrics displayed simultaneously
- **Enhanced Visualizations**: Larger graphs with more data points
- **Comparative Views**: Side-by-side session comparisons

#### Settings Screen (iPad)
**Portrait Mode**:
- **Two-Column Layout**: Settings categories and detailed configuration
- **Enhanced Forms**: Larger form controls and input fields
- **Preview Panels**: Live preview of configuration changes
- **Expanded Options**: More configuration options visible simultaneously

**Landscape Mode**:
- **Three-Column Layout**: Categories, settings, and preview/help
- **Contextual Help**: Dedicated help panel explaining settings
- **Advanced Controls**: More sophisticated configuration interfaces
- **Real-time Validation**: Immediate feedback on parameter changes

### iPad-Specific UI Components

#### Enhanced Circular Progress View
```swift
struct iPadCircularProgressView: View {
    let size: CGFloat = 450 // Larger for iPad
    let strokeWidth: CGFloat = 20 // Thicker stroke
    
    var body: some View {
        ZStack {
            // Enhanced background with multiple rings
            // Larger pressure value display
            // Additional zone indicators
            // Real-time trend indicators
        }
    }
}
```

#### Multi-Column Session Cards
```swift
struct iPadSessionCard: View {
    var body: some View {
        HStack(spacing: 24) {
            // Quality indicator (larger)
            // Session details (expanded)
            // Pressure chart (mini)
            // Duration breakdown
            // Performance metrics
        }
        .frame(height: 120) // Taller cards
    }
}
```

#### Adaptive Tab Bar / Sidebar
```swift
struct iPadNavigationView: View {
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    
    var body: some View {
        if horizontalSizeClass == .regular {
            // Sidebar navigation for landscape
            NavigationSplitView {
                sidebarContent
            } detail: {
                mainContent
            }
        } else {
            // Tab bar navigation for portrait
            TabView {
                // Standard tab structure
            }
        }
    }
}
```

### iPad Interaction Enhancements

#### Touch Targets
- **Minimum Size**: 44pt×44pt for all interactive elements
- **Preferred Size**: 60pt×60pt for primary actions
- **Spacing**: Minimum 8pt between adjacent touch targets
- **Hover Effects**: Support for Apple Pencil and trackpad hover states

#### Gesture Support
- **Pinch to Zoom**: Zoom into pressure charts and detailed views
- **Pan Gestures**: Navigate through session history with swipe gestures
- **Long Press**: Context menus for session management actions
- **Drag and Drop**: Reorder therapy blocks and configuration presets

#### Apple Pencil Support
- **Precise Selection**: Enhanced precision for chart interaction
- **Annotation**: Mark important points in session data
- **Pressure Sensitivity**: Use pencil pressure for demonstration purposes
- **Scribble Support**: Text input using Apple Pencil handwriting

### iPad Performance Optimizations

#### Memory Management
- **Larger Buffers**: Take advantage of iPad's additional RAM
- **Enhanced Caching**: Cache more session data for faster access
- **Background Processing**: More sophisticated background data processing
- **Multi-threading**: Better utilization of iPad's more powerful processors

#### Display Optimization
- **Higher Resolution**: Support for Retina display densities
- **ProMotion Support**: 120Hz refresh rate on compatible iPads
- **True Tone**: Adapt to ambient lighting conditions
- **Wide Color Gamut**: Enhanced color accuracy for medical applications

#### Audio Processing Enhancements
- **Multiple Microphones**: Support for iPad's multiple microphone array
- **Noise Cancellation**: Better environmental noise rejection
- **Spatial Audio**: Enhanced audio processing capabilities
- **External Microphones**: Support for professional external microphones

### iPad-Specific Features

#### Multi-Window Support
- **Split View**: Run alongside other medical apps
- **Slide Over**: Quick access while using other applications
- **Picture in Picture**: Maintain session monitoring in PiP mode
- **Multiple Windows**: Support for multiple Sada windows (iPadOS 13+)

#### External Display Support
- **AirPlay**: Mirror therapy sessions to external displays
- **USB-C/Thunderbolt**: Direct connection to external monitors
- **Presentation Mode**: Dedicated mode for clinical demonstrations
- **Extended Desktop**: Use external display for additional information

#### Keyboard and Trackpad Support
- **Hardware Keyboard**: Full keyboard shortcuts for navigation
- **Trackpad Gestures**: Support for Magic Trackpad gestures
- **Cursor Support**: Proper cursor interaction with all UI elements
- **Accessibility**: Enhanced keyboard navigation for accessibility

### iPad Data Visualization Enhancements

#### Advanced Charts and Graphs
- **Real-time Pressure Graphs**: Live updating pressure visualization
- **Multi-session Comparisons**: Side-by-side session analysis
- **Trend Analysis**: Long-term trend visualization with statistical analysis
- **Interactive Charts**: Zoom, pan, and drill-down capabilities

#### Enhanced Analytics Dashboard
```swift
struct iPadAnalyticsDashboard: View {
    var body: some View {
        LazyVGrid(columns: adaptiveColumns, spacing: 20) {
            AdherenceOverviewCard()
            QualityTrendsChart()
            PressureDistributionChart()
            BreathDurationAnalysis()
            WeeklyProgressChart()
            MonthlyComparisonView()
        }
        .padding()
    }
    
    private var adaptiveColumns: [GridItem] {
        [
            GridItem(.adaptive(minimum: 300, maximum: 400))
        ]
    }
}
```

#### Professional Reporting
- **PDF Export**: Generate professional therapy reports
- **Data Export**: Export session data in CSV/JSON formats
- **Print Support**: Direct printing of reports and charts
- **Share Extensions**: Share reports with healthcare providers

### iPad Accessibility Enhancements

#### VoiceOver Improvements
- **Enhanced Descriptions**: More detailed audio descriptions for charts
- **Spatial Audio**: Use spatial audio cues for data navigation
- **Custom Gestures**: iPad-specific VoiceOver gestures
- **Braille Support**: Enhanced braille display support

#### Visual Accessibility
- **Larger Text Support**: Better support for very large text sizes
- **High Contrast Mode**: Enhanced high contrast visualization
- **Zoom Integration**: Better integration with system zoom features
- **Color Differentiation**: Additional visual cues beyond color

#### Motor Accessibility
- **Switch Control**: Full switch control navigation support
- **Voice Control**: Enhanced voice control for hands-free operation
- **AssistiveTouch**: Optimized for AssistiveTouch usage
- **Dwell Control**: Support for dwell-based interaction

### iPad Testing Requirements

#### Device Testing Matrix
- **iPad (9th generation)**: Baseline performance testing
- **iPad Air (4th/5th generation)**: Standard performance validation
- **iPad Pro (11"/12.9")**: Advanced feature testing
- **iPad mini**: Compact iPad layout validation

#### Orientation Testing
- **Portrait Mode**: All features functional in portrait
- **Landscape Mode**: Enhanced landscape-specific features
- **Rotation Handling**: Smooth transitions between orientations
- **Split View**: Proper behavior in split-screen scenarios

#### Performance Benchmarks
- **Audio Processing**: <50ms latency on all supported iPads
- **UI Responsiveness**: 60fps (120fps on ProMotion displays)
- **Memory Usage**: <200MB typical, <400MB peak
- **Battery Life**: Minimal impact on iPad battery performance

### iPad-Specific Configuration

#### Algorithm Optimizations
- **Enhanced Processing**: Take advantage of iPad's more powerful processors
- **Larger Buffers**: Use additional RAM for better accuracy
- **Multi-core Processing**: Utilize multiple CPU cores for signal processing
- **GPU Acceleration**: Use Metal for computationally intensive operations

#### iPad Configuration Presets
```swift
extension AlgorithmConfiguration {
    static let iPadOptimized = AlgorithmConfiguration(
        sampleRate: 48000.0,  // Higher sample rate
        bufferSize: 0.05,     // Smaller buffer for lower latency
        targetBufferLength: 600,  // Larger buffer for better accuracy
        correlationThreshold: 0.7,  // Higher threshold for precision
        // Additional iPad-specific optimizations
    )
}
```

### iPad Development Guidelines

#### SwiftUI Adaptations
```swift
// Adaptive layouts for iPad
struct AdaptiveTherapyView: View {
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    @Environment(\.verticalSizeClass) var verticalSizeClass
    
    var body: some View {
        if horizontalSizeClass == .regular && verticalSizeClass == .regular {
            // iPad-specific layout
            iPadTherapyLayout()
        } else {
            // iPhone layout
            iPhoneTherapyLayout()
        }
    }
}
```

#### Size Class Handling
- **Regular/Regular**: Full iPad interface with all enhancements
- **Compact/Regular**: iPad in split view or slide over
- **Regular/Compact**: iPad in landscape with limited height
- **Compact/Compact**: Fallback to iPhone-style interface

#### Multi-Window Architecture
```swift
@main
struct SadaApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
        
        // iPad-specific window support
        #if os(iOS)
        WindowGroup("Session Monitor") {
            SessionMonitorView()
        }
        .handlesExternalEvents(matching: Set(arrayLiteral: "session-monitor"))
        #endif
    }
}
```

---

## Audio Processing Requirements

### Research Foundation
The audio processing algorithms are based on published research with the following specifications:
- **Correlation Coefficient**: r² = 0.886 between frequency and pressure
- **Data Points**: Validated across 9,993 measurements
- **Linear Model**: Pressure = -4.659 + 1.119 × Frequency (Hz)

### Signal Processing Pipeline

#### Step 1: Audio Capture
- **Sample Rate**: 44.1 kHz (configurable)
- **Buffer Size**: 0.1 seconds (4,410 samples)
- **Format**: 32-bit float, mono channel
- **Permissions**: Request microphone access with user consent

#### Step 2: Preprocessing
- **High-pass Filtering**: Remove DC offset and low-frequency noise
- **Smoothing Filter**: Moving average with configurable window size
- **Signal Subtraction**: Remove smoothed version from raw signal

#### Step 3: Signal Conditioning
- **Squaring**: Compute power of filtered signal
- **Downsampling**: Reduce sample rate by factor of 45 (44.1kHz → 980Hz)
- **Gaussian Smoothing**: Apply Gaussian kernel for noise reduction

#### Step 4: Pitch Detection (Two-Stage Autocorrelation)
**Coarse Search**:
```swift
func coarseAutocorrelationSearch() {
    let coarseStep = 3  // Paper specification
    let targetMinPeriod = Int(downsampledRate / Float(maxFreq))
    let targetMaxPeriod = Int(downsampledRate / Float(minFreq))
    
    var maxCorrelation: Float = 0.0
    var bestPeriod: Int = 0
    
    for period in stride(from: targetMinPeriod, through: targetMaxPeriod, by: coarseStep) {
        let correlation = calculateAutocorrelation(for: period)
        if correlation > maxCorrelation {
            maxCorrelation = correlation
            bestPeriod = period
        }
    }
    
    // Apply correlation threshold (0.6 paper spec)
    if maxCorrelation >= correlationThreshold {
        performFineSearch(around: bestPeriod)
    }
}
```

**Fine Search**:
```swift
func fineAutocorrelationSearch(around coarsePeriod: Int) {
    let fineWindow = 5  // ±5 samples around coarse result
    let searchStart = max(coarsePeriod - fineWindow, targetMinPeriod)
    let searchEnd = min(coarsePeriod + fineWindow, targetMaxPeriod)
    
    var maxCorrelation: Float = 0.0
    var bestPeriod: Int = 0
    
    for period in searchStart...searchEnd {
        let correlation = calculateAutocorrelation(for: period)
        if correlation > maxCorrelation {
            maxCorrelation = correlation
            bestPeriod = period
        }
    }
    
    // Apply parabolic interpolation for sub-sample accuracy
    let interpolatedPeriod = parabolicInterpolation(bestPeriod, correlation: maxCorrelation)
    let frequency = downsampledRate / interpolatedPeriod
    
    // Convert to pressure using linear model
    let pressure = pressureSlope * frequency + pressureIntercept
}
```

**Autocorrelation Function**:
```swift
func calculateAutocorrelation(for period: Int) -> Float {
    let bufferLength = processedBuffer.count
    let maxLag = min(period, bufferLength - period)
    
    var sum: Float = 0.0
    var count: Int = 0
    
    for i in 0..<maxLag {
        sum += processedBuffer[i] * processedBuffer[i + period]
        count += 1
    }
    
    return count > 0 ? sum / Float(count) : 0.0
}
```

**Economical Search Optimization**:
- Cache previous period results to reduce computation
- Use adaptive search window based on frequency stability
- Skip periods with insufficient correlation improvement

#### Step 5: Pressure Conversion
- **Linear Model**: Apply research-validated conversion formula
- **Range Validation**: Clamp output to 0-30 cm H₂O
- **Confidence Scoring**: Calculate reliability based on frequency stability

### Performance Optimization
- **Buffer Management**: Sliding window approach for continuous processing
- **Economical Search**: Cache previous results to reduce computation
- **Moving Averages**: Smooth output with configurable decay rates
- **Run Length Limiting**: Prevent excessive smoothing with leap detection

### Configuration Parameters
The system includes 50+ tunable parameters organized into categories:

#### Complete Parameter Reference
```swift
struct AlgorithmConfiguration {
    // MARK: - Audio Input Parameters
    var sampleRate: Float = 44100.0           // Hz (22050-48000)
    var bufferSize: Float = 0.1               // seconds (0.05-0.25)
    
    // MARK: - Frequency Detection Range
    var minFreq: Int = 7                      // Hz (5-15)
    var maxFreq: Int = 40                     // Hz (30-50)
    var freqAccuracy: Float = 0.025           // percentage (0.01-0.05)
    
    // MARK: - Signal Processing Parameters
    var lowerFormantFreq: Int = 250           // Hz (200-300)
    var minAmp: Float = 2.0e-4               // amplitude threshold
    var downsampleFactorOverride: Int? = nil  // nil for auto-calculation
    
    // MARK: - Autocorrelation Settings
    var correlationThreshold: Float = 0.6     // correlation (0.5-0.8)
    var coarseStep: Int = 3                   // search step (2-5)
    var fineSearchWindow: Int = 5             // window size (3-10)
    
    // MARK: - Moving Average Parameters
    var decayRate: Float = 0.8                // decay (0.7-0.9)
    var maxRunLength: Int = 5                 // max runs (3-10)
    var leapThreshold: Float = 0.20           // leap detection (0.1-0.3)
    
    // MARK: - Buffer Management
    var targetBufferLength: Int = 300         // samples (200-600)
    var minDataCheck: Int = 100               // minimum data (50-200)
    
    // MARK: - Pressure Model (Clinical)
    var pressureSlope: Float = 1.119          // linear slope
    var pressureIntercept: Float = -4.659     // linear intercept
    var minValidFreq: Float = 7.0             // Hz validation
    var maxValidFreq: Float = 40.0            // Hz validation
    var minValidPressure: Float = 6.0         // cm H₂O
    var maxValidPressure: Float = 30.0        // cm H₂O
    
    // MARK: - Advanced Settings
    var saveResults: Bool = false             // data logging
    var smoothingFilterSizeOverride: Int? = nil // filter override
}
```

#### Configuration Presets
The system provides 11 predefined configurations:

1. **Paper Compliant** - Strict research specifications
2. **Standard** - Default balanced settings
3. **Real-Time Feedback** - Low latency (50ms buffer, 0.5 correlation)
4. **High Accuracy** - Precision mode (200ms buffer, 0.7 correlation)
5. **Stable Readings** - Smooth output (0.9 decay, 7 max runs)
6. **Low Power** - Reduced CPU (22kHz sample rate)
7. **Noisy Environment** - Better noise rejection (10-35Hz range)
8. **Pediatric** - Children optimized (5-30Hz range, 0.55 correlation)
9. **Elderly/COPD** - Stability focus (8-35Hz range, 0.85 decay)
10. **Athletic** - High performance (10-45Hz range, precision)
11. **Research** - Clinical study (48kHz, 250ms buffer, logging)

---

## Data Management

### Data Models

#### CompletedSession
```swift
struct CompletedSession: Codable, Identifiable {
    let id: UUID
    let date: Date
    let duration: TimeInterval
    let quality: SessionQuality
    let stepsCompleted: Int
    let totalSteps: Int
    let averagePressure: Double
    let maxPressure: Double
    let minPressure: Double
    let pressureReadings: [StoredPressureReading]
    let feedback: String
    let actualBreathDurations: [Double]
    
    // Additional fields in actual implementation
    let version: String = "1.0"
    let deviceModel: String
    let algorithmPreset: String
    let notes: String?
    let rawAudioStats: AudioProcessingStats?
    
    init(from therapySession: TherapySession) {
        self.id = UUID()
        self.date = Date()
        self.duration = therapySession.sessionDuration
        self.quality = therapySession.sessionQuality
        self.stepsCompleted = therapySession.currentStep
        self.totalSteps = therapySession.totalSteps
        
        // Calculate pressure statistics
        let pressures = therapySession.pressureReadings.map { $0.pressure }
        self.averagePressure = pressures.isEmpty ? 0.0 : pressures.reduce(0, +) / Double(pressures.count)
        self.maxPressure = pressures.max() ?? 0.0
        self.minPressure = pressures.min() ?? 0.0
        
        // Convert pressure readings to stored format
        self.pressureReadings = therapySession.pressureReadings.map { reading in
            StoredPressureReading(
                timestamp: reading.timestamp,
                pressure: reading.pressure,
                step: reading.step
            )
        }
        
        self.feedback = therapySession.feedback
        self.actualBreathDurations = therapySession.recordedBreathDurations
        self.deviceModel = UIDevice.current.model
        self.algorithmPreset = therapySession.algorithmPreset ?? "standard"
        self.notes = nil
        self.rawAudioStats = nil
    }
}
```

#### TherapyBlock
```swift
struct TherapyBlock {
    let id: Int
    var exhalations: Int
    var name: String
    var isCompleted: Bool
    var completedAt: Date?
}
```

#### PressureReading
```swift
struct PressureReading {
    let timestamp: Date
    let pressure: Double
    let step: Int
}
```

### Storage Strategy

#### File-Based Storage
```
Documents/
├── TherapySessions/
│   ├── recent_sessions.json           // Last 30 days (in memory)
│   ├── archive_2024-01.json           // Monthly archives
│   ├── archive_2024-02.json
│   ├── tmp/                           // Staging for atomic writes
│   │   ├── session_write_tmp.json
│   │   └── archive_tmp.json
│   └── backup/                        // Backup copies
│       ├── recent_sessions_backup.json
│       └── archive_backup_2024-01.json
├── Configuration/
│   ├── therapy_config.json            // Daily therapy settings
│   ├── algorithm_config.json          // Algorithm parameters
│   └── user_preferences.json          // UI preferences
└── Logs/                              // Debug logs (if enabled)
    ├── session_log_2024-01-15.log
    └── audio_processing_log.log
```

**File Format Specifications**:
```json
{
  "schema": "2.0",
  "version": "1.0.0",
  "timestamp": "2024-01-15T14:30:00Z",
  "device": {
    "model": "iPhone 15 Pro",
    "os": "iOS 17.2",
    "appVersion": "1.0.0"
  },
  "sessions": [
    {
      "id": "UUID-string",
      "date": "2024-01-15T14:30:00Z",
      "duration": 180.5,
      "quality": "good",
      "stepsCompleted": 10,
      "totalSteps": 10,
      "averagePressure": 15.2,
      "maxPressure": 18.5,
      "minPressure": 12.1,
      "algorithmPreset": "standard",
      "pressureReadings": [
        {
          "timestamp": "2024-01-15T14:30:05Z",
          "pressure": 15.2,
          "step": 1,
          "frequency": 19.8,
          "correlation": 0.75
        }
      ],
      "actualBreathDurations": [2.1, 2.3, 1.8, 2.5],
      "feedback": "Excellent session!",
      "notes": null,
      "encryption": "AES256" // if HIPAA mode enabled
    }
  ]
}
```

**Data Protection**:
- **File Protection**: `NSFileProtection.completeUntilFirstUserAuthentication`
- **Encryption**: AES-256 encryption for HIPAA compliance mode
- **Atomic Writes**: Use temporary files for safe data updates
- **Backup Strategy**: Automatic backup copies before major operations

#### Data Lifecycle
1. **Active Sessions**: Stored in memory during app use
2. **Recent History**: Last 30 days kept in memory for fast access
3. **Archive Storage**: Older sessions moved to monthly files
4. **Cleanup**: Data older than 1 year automatically deleted

#### Migration Strategy
- **UserDefaults Migration**: Automatic migration from legacy storage
- **Version Compatibility**: Backward-compatible data formats
- **Error Recovery**: Graceful handling of corrupted data

### Analytics & Metrics

#### Session Quality Calculation
```swift
enum SessionQuality {
    case excellent  // 80%+ time in target zone
    case good      // 60-79% time in target zone
    case fair      // 40-59% time in target zone
    case poor      // <40% time in target zone
    case none      // No data available
}
```

#### Adherence Metrics
- **Daily Progress**: Completed vs. planned sessions
- **Weekly Trends**: Session frequency and quality
- **Monthly Summaries**: Long-term adherence patterns
- **Quality Distribution**: Breakdown by session quality

#### Performance Indicators
- **Green Zone Percentage**: Time spent in optimal pressure range
- **Average Exhalation Duration**: Breath timing consistency
- **Zone Consistency Score**: Stability of pressure maintenance
- **Breath Duration Variance**: Consistency of breathing pattern

---

## Configuration System

### Therapy Configuration

#### Daily Block Management
- **Block Structure**: ID, exhalations count, name, completion status
- **Preset Options**: 1-3 sessions per day configurations
- **Validation**: Enforce medical safety limits
- **Persistence**: Automatic saving of configuration changes

#### Session Presets
1. **Single Session**: 1 block of 10 exhalations
2. **Two Sessions**: 2 blocks of 15 exhalations each
3. **Three Sessions**: 3 blocks of 10 exhalations each (default)

#### Daily Reset Logic
- **Automatic Reset**: New day detection based on last session date
- **Progress Tracking**: Maintain current block index and completion status
- **Session Continuity**: Handle app backgrounding and restoration

### Algorithm Configuration

#### Configuration Presets
1. **Paper Compliant**: Strict research specifications
2. **Standard**: Balanced performance and accuracy
3. **Real-Time Feedback**: Low latency, faster updates
4. **High Accuracy**: Maximum precision, higher latency
5. **Stable Readings**: Smooth output, slower response
6. **Low Power**: Reduced CPU usage
7. **Noisy Environment**: Enhanced noise rejection
8. **Pediatric**: Optimized for children
9. **Elderly/COPD**: Stability-focused for older patients
10. **Athletic**: High-performance mode
11. **Research/Clinical**: Maximum data collection

#### Parameter Categories
- **Audio Input**: Sample rates, buffer sizes, format settings
- **Frequency Detection**: Range limits, accuracy targets
- **Signal Processing**: Filter configurations, processing steps
- **Autocorrelation**: Search parameters, thresholds
- **Moving Averages**: Smoothing settings, adaptation rates
- **Pressure Model**: Conversion parameters, validation ranges

#### Runtime Configuration
- **Hot Swapping**: Change parameters without restart
- **Validation**: Automatic parameter consistency checking
- **Performance Impact**: Real-time performance monitoring
- **Preset Management**: Save and load custom configurations

---

## Quality Assurance

### Breath Detection Quality

#### Validation Criteria
- **Minimum Duration**: 1.5 seconds for valid breath
- **Signal Strength**: Adequate amplitude for reliable detection
- **Frequency Stability**: Consistent pitch throughout breath
- **Noise Rejection**: Filter out environmental sounds

#### Performance Classification
- **Green (Excellent)**: 3+ seconds, mostly in target zone
- **Amber (Good)**: 1.5-3 seconds OR 3+ seconds with some deviation
- **Red (Poor)**: 3+ seconds with significant time outside target zone

#### Quality Metrics
- **Breath Count Accuracy**: Prevent double-counting and false positives
- **Duration Measurement**: Precise timing of breath events
- **Pressure Accuracy**: Reliable conversion from audio to pressure
- **Real-time Performance**: Minimal latency in feedback

### Session Quality Assessment (Simplified)

#### Simplified Quality Scoring
```swift
enum SessionQuality: Int, CaseIterable {
    case excellent = 5  // 80%+ time in target zone
    case good = 4       // 60-79% time in target zone  
    case fair = 3       // 40-59% time in target zone
    case poor = 2       // 20-39% time in target zone
    case veryPoor = 1   // <20% time in target zone
    
    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .mint
        case .fair: return .yellow
        case .poor: return .orange
        case .veryPoor: return .red
        }
    }
    
    var icon: String {
        switch self {
        case .excellent: return "checkmark.circle.fill"
        case .good: return "checkmark.circle"
        case .fair: return "minus.circle"
        case .poor: return "exclamationmark.circle"
        case .veryPoor: return "xmark.circle"
        }
    }
}
```

#### Essential Metrics Only
**Core Tracking (Simplified)**:
- **Completion Rate**: Did they finish the session? (Yes/No)
- **Quality Score**: Simple 1-5 scale based on time in target zone
- **Consistency Score**: Unified metric (0-100) combining adherence and quality
- **Streak Count**: Consecutive days with completed sessions

**Eliminated Complex Metrics**:
- No breath duration variance analysis
- No pressure zone distribution charts
- No complex statistical calculations
- No multi-dimensional quality matrices

---

## Performance Requirements

### Real-Time Performance
- **Audio Processing Latency**: <100ms from input to display
- **UI Update Frequency**: 60 FPS smooth animations
- **Memory Usage**: <50MB typical, <100MB peak
- **CPU Usage**: <30% average on iPhone 12 or newer

### Battery Optimization
- **Background Processing**: Minimal when app not active
- **Audio Engine Management**: Efficient start/stop cycles
- **Display Optimization**: Dark theme reduces OLED power consumption
- **Computation Efficiency**: Optimized algorithms for mobile processors

### Scalability
- **Data Storage**: Efficient archiving for long-term use
- **Algorithm Performance**: Maintains speed with parameter changes
- **UI Responsiveness**: Smooth operation across device sizes
- **Memory Management**: Automatic cleanup of old data

### Device Compatibility
- **Minimum Requirements**: iPhone 8, iOS 15.0
- **Optimal Performance**: iPhone 12 or newer
- **Screen Adaptation**: Support for all iPhone screen sizes
- **Audio Hardware**: Compatible with built-in microphones

---

## Build & Deployment Requirements

### Xcode Project Configuration
```
Sada.xcodeproj
├── Targets:
│   ├── Sada                    // Main app target
│   ├── SadaTests              // Unit tests
│   └── SadaUITests            // UI tests
├── Configurations:
│   ├── Debug                     // Development builds
│   ├── Release                   // App Store builds
│   └── Enterprise               // Clinical builds
└── Schemes:
    ├── Sada                   // Standard scheme
    ├── Sada-Debug            // Debug with logging
    └── Sada-Research         // Research mode
```

### Required Entitlements
```xml
<key>com.apple.security.device.microphone</key>
<true/>
<key>com.apple.security.device.audio-input</key>
<true/>
<key>com.apple.security.app-sandbox</key>
<false/>
<key>com.apple.security.files.user-selected.read-write</key>
<true/>
```

### Build Commands
```bash
# Build for device
xcodebuild -scheme Sada -destination 'platform=iOS Simulator,name=iPhone 15' build

# Run tests
xcodebuild -scheme Sada -destination 'platform=iOS Simulator,name=iPhone 15' test

# Clean build
xcodebuild -scheme Sada clean

# Archive for distribution
xcodebuild -scheme Sada -archivePath "Sada.xcarchive" archive
```

### Dependencies
```swift
// Package.swift dependencies
dependencies: [
    .package(url: "https://github.com/apple/swift-algorithms", from: "1.0.0"),
    .package(url: "https://github.com/apple/swift-numerics", from: "1.0.0"),
]

// Framework dependencies
import Foundation
import AVFoundation
import Accelerate
import SwiftUI
import Combine
```

### MCP Tools Integration

#### Magic UI MCP for Interface Design
**Purpose**: Use Magic UI MCP components for modern, animated, and interactive UI elements

**Required Integration**:
- **Always consult Magic UI MCP** before implementing custom UI components
- **Use provided components** for enhanced user experience and modern design patterns
- **Check component availability** for specific UI needs during development

**Recommended Magic UI Components for Sada**:

**Progress & Analytics**:
- `animated-circular-progress-bar` - For weekly progress rings and session completion indicators
- `number-ticker` - For animated metrics (streak counters, completion percentages)
- `sparkles-text` - For achievement celebrations and positive feedback

**Cards & Layout**:
- `magic-card` - For session history cards with hover effects
- `bento-grid` - For dashboard layout on larger screens
- `animated-list` - For session history lists with smooth animations

**Interactive Elements**:
- `shimmer-button` - For primary action buttons (Start Session)
- `ripple-button` - For secondary actions and confirmations
- `animated-subscribe-button` - For settings toggles and preferences

**Feedback & Celebrations**:
- `confetti` - For milestone achievements and session completions
- `particles` - For subtle background effects during therapy sessions
- `animated-beam` - For connecting therapy blocks visually

**Text & Typography**:
- `animated-gradient-text` - For app branding and important messages
- `blur-fade` - For smooth content transitions
- `text-reveal` - For progressive disclosure of information

#### Context7 MCP for Documentation
**Purpose**: Always use Context7 MCP to access the latest documentation for dependencies and frameworks

**Required Workflow**:
1. **Before implementing any feature** → Check Context7 MCP for latest documentation
2. **When adding new dependencies** → Use Context7 MCP to get current best practices
3. **During troubleshooting** → Consult Context7 MCP for updated solutions
4. **For framework updates** → Use Context7 MCP to understand breaking changes

**Key Libraries to Monitor**:
- SwiftUI updates and new components
- AVFoundation changes for audio processing
- Accelerate framework optimizations
- iOS SDK updates and deprecations

**Integration Examples**:
```swift
// Before implementing audio features
// Use: Context7 MCP → query "AVFoundation audio processing iOS 17"

// Before adding animations
// Use: Context7 MCP → query "SwiftUI animations iOS 17 best practices"

// Before implementing charts
// Use: Context7 MCP → query "Swift Charts framework latest features"
```

---

## Security & Privacy

### Data Privacy
- **Local Storage Only**: No cloud synchronization or external transmission
- **Health Data Protection**: Treat all therapy data as sensitive health information
- **User Consent**: Clear disclosure of data collection and usage
- **Data Minimization**: Collect only necessary information for therapy

### Microphone Privacy
- **Permission Requests**: Clear explanation of microphone usage
- **Usage Indicators**: Visual feedback when microphone is active
- **Data Processing**: Real-time processing without permanent audio storage
- **User Control**: Easy microphone permission management

### Data Security
- **File System Protection**: Use iOS file protection APIs
- **Data Encryption**: Encrypt sensitive data at rest
- **Access Control**: Restrict data access to app sandbox
- **Secure Deletion**: Proper cleanup when data is removed

### Compliance Considerations
- **HIPAA Awareness**: Design with healthcare privacy principles
- **Medical Device Guidelines**: Follow FDA guidance for medical apps
- **App Store Requirements**: Comply with health app review guidelines
- **International Standards**: Consider global privacy regulations

---

## Development Guidelines

### Code Architecture

#### Project Structure
```
Sada/
├── Sada/
│   ├── App/
│   │   ├── SadaApp.swift
│   │   └── ContentView.swift
│   ├── Views/
│   │   ├── TabBarView.swift
│   │   ├── TherapyView.swift
│   │   ├── HistoryView.swift
│   │   └── SettingsView.swift
│   ├── Models/
│   │   ├── TherapySession.swift
│   │   ├── TherapyConfiguration.swift
│   │   └── CompletedSession.swift
│   ├── Audio/
│   │   ├── AudioManager.swift
│   │   ├── PitchDetector.swift
│   │   ├── PressureCalculator.swift
│   │   └── BreathDetector.swift
│   ├── Configuration/
│   │   ├── AlgorithmConfiguration.swift
│   │   └── ConfigurationManager.swift
│   ├── Data/
│   │   └── SessionHistoryManager.swift
│   └── Supporting Files/
├── SadaTests/
└── SadaUITests/
```

#### Coding Standards
- **Naming Conventions**: PascalCase for types, camelCase for variables/functions
- **Import Order**: Foundation, AVFoundation, SwiftUI, then local imports
- **Documentation**: Header comments with author/date, inline comments for complex logic
- **Error Handling**: Comprehensive error handling with proper throw/catch patterns
- **Code Organization**: Use `// MARK: - Section` for logical grouping
- **MCP Integration**: Always consult Magic UI MCP and Context7 MCP during development

#### Architecture Patterns
- **MVVM**: Model-View-ViewModel with ObservableObject
- **Dependency Injection**: Pass dependencies through initializers
- **Protocol-Oriented**: Use protocols for testability and flexibility
- **Reactive Programming**: Combine framework for data flow
- **MCP-First Development**: Always consult MCP tools before implementation

#### MCP-Driven Development Workflow
**Pre-Development Phase**:
1. **UI Component Research**: Check Magic UI MCP for available components before creating custom ones
2. **Framework Documentation**: Use Context7 MCP to get latest documentation for all dependencies
3. **Design System Planning**: Leverage Magic UI components for consistent, modern design patterns

**Implementation Examples**:
```swift
// History Screen Progress Ring Implementation
// 1. Check Magic UI MCP for animated-circular-progress-bar
// 2. Use Context7 MCP to verify SwiftUI animation best practices

struct WeeklyProgressRing: View {
    @State private var progress: Double = 0.0
    
    var body: some View {
        VStack(spacing: 16) {
            // Magic UI animated-circular-progress-bar
            AnimatedCircularProgressBar(
                progress: progress,
                color: .sage,
                lineWidth: 8,
                size: 120
            )
            .onAppear {
                withAnimation(.easeInOut(duration: 1.0)) {
                    progress = weeklyCompletionRate
                }
            }
            
            Text("This Week")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// Session Cards with Magic UI
// Use magic-card component for hover effects
struct SessionCard: View {
    var body: some View {
        MagicCard {
            HStack {
                QualityIndicator(quality: session.quality)
                VStack(alignment: .leading) {
                    Text(session.date.formatted(.dateTime.weekday().hour().minute()))
                    Text("\(session.duration.formatted(.number.precision(.fractionLength(0)))) min")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                Spacer()
            }
            .padding()
        }
    }
}
```

**Quality Assurance**:
- **Component Validation**: Ensure all Magic UI components are properly integrated
- **Documentation Compliance**: Verify implementation follows latest Context7 MCP guidelines
- **Performance Testing**: Test Magic UI animations on target devices

### SwiftUI Best Practices

#### State Management
```swift
// Published properties for UI updates
@Published var currentPressure: Double = 0.0
@Published var sessionProgress: Double = 0.0

// Environment objects for shared state
@EnvironmentObject var historyManager: SessionHistoryManager
@EnvironmentObject var therapyConfiguration: TherapyConfiguration

// State objects for view-owned data
@StateObject private var audioManager = AudioManager()
```

#### View Composition
- **Small, Focused Views**: Break complex views into smaller components
- **Reusable Components**: Create generic components for common UI patterns
- **Conditional Rendering**: Use proper conditional view rendering
- **Animation**: Smooth transitions with appropriate animation curves

#### Performance Optimization
- **Lazy Loading**: Use LazyVStack/LazyHStack for large lists
- **View Updates**: Minimize unnecessary view updates with proper state management
- **Memory Management**: Proper cleanup in deinit methods
- **Background Processing**: Use appropriate queues for heavy computations

### Audio Processing Guidelines

#### AVFoundation Integration
```swift
// Audio session configuration
let audioSession = AVAudioSession.sharedInstance()
try audioSession.setCategory(.playAndRecord, mode: .measurement, options: [.defaultToSpeaker])

// Audio engine setup
let audioEngine = AVAudioEngine()
let inputNode = audioEngine.inputNode
inputNode.installTap(onBus: 0, bufferSize: bufferSize, format: inputFormat) { buffer, time in
    processAudioBuffer(buffer)
}
```

#### Signal Processing Best Practices
- **Buffer Management**: Efficient buffer allocation and reuse
- **Thread Safety**: Proper synchronization between audio and UI threads
- **Error Recovery**: Graceful handling of audio interruptions
- **Performance Monitoring**: Track processing time and optimize bottlenecks

---

## Testing Strategy

### Unit Testing

#### Core Algorithm Testing
```swift
class PitchDetectionTests: XCTestCase {
    func testFrequencyDetection() {
        // Test pitch detection accuracy
        // Verify frequency range validation
        // Check correlation thresholds
    }
    
    func testPressureCalculation() {
        // Test linear model accuracy
        // Verify range clamping
        // Check edge cases
    }
}
```

#### Data Model Testing
- **Session Management**: Test session lifecycle and state transitions
- **Configuration Validation**: Verify parameter constraints and validation
- **Data Persistence**: Test save/load operations and data integrity
- **Migration Logic**: Verify data migration from legacy formats

#### Audio Processing Testing
- **Mock Audio Input**: Test with synthetic audio signals
- **Algorithm Validation**: Verify against known reference data
- **Performance Testing**: Measure processing time and memory usage
- **Edge Case Handling**: Test with various audio conditions

### Integration Testing

#### End-to-End Workflows
- **Complete Therapy Session**: From start to completion with data saving
- **Configuration Changes**: Runtime parameter updates and effects
- **Data Migration**: Legacy data import and format conversion
- **Permission Handling**: Microphone access and error scenarios

#### UI Testing
```swift
class SadaUITests: XCTestCase {
    func testTherapySessionFlow() {
        // Test complete session workflow
        // Verify UI state transitions
        // Check accessibility compliance
    }
}
```

### Performance Testing

#### Audio Processing Performance
- **Latency Measurement**: Input to output delay
- **CPU Usage Monitoring**: Processing load under various conditions
- **Memory Profiling**: Memory allocation and cleanup
- **Battery Impact**: Power consumption during active use

#### UI Performance Testing
- **Frame Rate Monitoring**: Maintain 60 FPS during animations
- **Responsiveness Testing**: UI interaction response times
- **Memory Usage**: View controller lifecycle and memory management
- **Device Compatibility**: Performance across different iPhone models

### Accessibility Testing

#### VoiceOver Support
- **Screen Reader Compatibility**: All UI elements properly labeled
- **Navigation Flow**: Logical tab order and focus management
- **Audio Feedback**: Alternative feedback for visual elements
- **Gesture Support**: VoiceOver gesture compatibility

#### Visual Accessibility
- **High Contrast Support**: Readable in high contrast mode
- **Dynamic Type**: Support for larger text sizes
- **Color Blindness**: Information not conveyed by color alone
- **Reduced Motion**: Respect reduced motion preferences

---

## Deployment Requirements

### App Store Preparation

#### App Information
- **App Name**: Sada
- **Category**: Medical
- **Age Rating**: 4+ (suitable for all ages)
- **Keywords**: PEP therapy, respiratory, breathing, medical device
- **Description**: Professional PEP therapy guidance with real-time feedback

#### Medical App Compliance
- **FDA Guidance**: Follow FDA recommendations for medical apps
- **Health Claims**: Avoid making medical claims without proper validation
- **Professional Use**: Clearly indicate intended for use under medical supervision
- **Disclaimer**: Include appropriate medical disclaimers

#### App Store Review Guidelines
- **Health and Medical**: Comply with App Store health app requirements
- **Privacy Policy**: Comprehensive privacy policy for health data
- **User Safety**: Clear instructions and safety warnings
- **Professional Review**: Consider medical professional review before submission

### Build Configuration

#### Release Configuration
```swift
// Build settings for release
SWIFT_COMPILATION_MODE = wholemodule
SWIFT_OPTIMIZATION_LEVEL = -O
GCC_OPTIMIZATION_LEVEL = s
ENABLE_BITCODE = YES
```

#### Code Signing
- **Development Team**: Proper team configuration
- **Provisioning Profiles**: Distribution profile for App Store
- **Certificates**: Valid distribution certificate
- **Entitlements**: Microphone usage entitlement

#### Version Management
- **Semantic Versioning**: Major.Minor.Patch format
- **Build Numbers**: Increment for each build
- **Release Notes**: Clear changelog for each version
- **Beta Testing**: TestFlight distribution for pre-release testing

### Distribution Strategy

#### Initial Release
- **Soft Launch**: Limited geographic release for initial feedback
- **Medical Community**: Engage with healthcare professionals for validation
- **User Feedback**: Collect and analyze user feedback for improvements
- **Performance Monitoring**: Track app performance and crash reports

#### Update Strategy
- **Regular Updates**: Monthly updates with improvements and bug fixes
- **Feature Releases**: Quarterly major feature additions
- **Security Updates**: Immediate updates for security issues
- **Compatibility**: Maintain compatibility with latest iOS versions

---

## Success Metrics

### User Engagement
- **Daily Active Users**: Users completing therapy sessions daily
- **Session Completion Rate**: Percentage of started sessions completed
- **Adherence Rate**: Planned vs. actual session completion
- **User Retention**: 7-day, 30-day, and 90-day retention rates

### Clinical Effectiveness
- **Session Quality Trends**: Improvement in session quality over time
- **Adherence Improvement**: Increased therapy compliance
- **User Satisfaction**: Ratings and feedback from users and healthcare providers
- **Clinical Outcomes**: Correlation with improved respiratory function (where data available)

### Technical Performance
- **App Performance**: Crash rate <0.1%, load time <3 seconds
- **Audio Processing Accuracy**: Maintain research-validated correlation levels
- **Battery Efficiency**: Minimal impact on device battery life
- **Device Compatibility**: Support across all target iPhone models

### Business Metrics
- **App Store Rating**: Maintain 4.5+ star rating
- **Download Growth**: Steady increase in app downloads
- **Healthcare Adoption**: Adoption by healthcare institutions
- **User Feedback**: Positive feedback from medical professionals

---

## Future Enhancements

### Planned Features
1. **Apple Health Integration**: Sync therapy data with Health app
2. **Healthcare Provider Dashboard**: Web portal for clinician monitoring
3. **Advanced Analytics**: Machine learning for personalized therapy optimization
4. **Multi-language Support**: Localization for international markets
5. **Apple Watch Companion**: Simplified interface for wearable devices

### Technical Improvements
1. **Cloud Synchronization**: Optional cloud backup and sync
2. **Advanced Algorithms**: Enhanced pitch detection with machine learning
3. **Calibration System**: User-specific calibration for improved accuracy
4. **Integration APIs**: Healthcare system integration capabilities
5. **Telemedicine Support**: Video consultation integration

### Research Opportunities
1. **Clinical Studies**: Collaborate on effectiveness research
2. **Algorithm Validation**: Continuous improvement of audio-to-pressure conversion
3. **User Behavior Analysis**: Optimize therapy adherence strategies
4. **Device Compatibility**: Expand to additional platforms and devices

---

## Conclusion

Sada represents a sophisticated medical application that combines advanced audio signal processing with intuitive user interface design to provide effective PEP therapy guidance. The application's foundation in published research, comprehensive configuration system, and focus on user experience make it a valuable tool for respiratory therapy.

The technical architecture emphasizes real-time performance, clinical accuracy, and user safety while maintaining the flexibility needed for diverse patient populations. The comprehensive testing strategy and deployment requirements ensure a reliable, professional-grade medical application suitable for healthcare environments.

This PRD provides the complete specification needed to recreate the Sada application, including all technical details, user interface requirements, and implementation guidelines necessary for successful development and deployment.

---

*Document Version: 1.0*  
*Last Updated: January 2025*  
*Total Pages: 25*