//
//  ImprovedBreathDetector.swift
//  Breath2
//
//  More reliable breath detection with smoothing and hysteresis
//

import Foundation
import SwiftUI

class ImprovedBreathDetector: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentState: BreathState = .idle
    @Published var breathCount: Int = 0
    @Published var currentBreathDuration: TimeInterval = 0
    @Published var isBreathing: Bool = false
    @Published var lastBreathEvent: BreathEvent?
    
    // MARK: - Configuration
    
    private var config: BreathDetectionConfig = .standard
    
    // MARK: - Computed Parameters (from config)
    
    private var smoothingWindowSize: Int { config.smoothingWindowSize }
    private var confidenceThreshold: Float { config.confidenceThreshold }
    private var startAudioThreshold: Float { config.startAudioThreshold }
    private var continueAudioThreshold: Float { config.continueAudioThreshold }
    private var startPressureThreshold: Float { config.startPressureThreshold }
    private var continuePressureThreshold: Float { config.continuePressureThreshold }
    private var minimumBreathDuration: TimeInterval { config.minimumBreathDuration }
    private var maxSilenceGap: TimeInterval { config.maxSilenceGap }
    private var minimumBreathInterval: TimeInterval { config.minimumBreathInterval }
    private var confirmationDelay: TimeInterval { config.confirmationDelay }
    
    // MARK: - Smoothing State
    
    private var audioLevelHistory: [Float] = []
    private var pressureHistory: [Float] = []
    private var frequencyHistory: [Float] = []
    private var confidenceHistory: [Float] = []
    
    // MARK: - Detection State
    
    private var currentBreathStartTime: Date?
    private var lastSignificantSignalTime: Date?
    private var amplitudeHistory: [Float] = []
    private var lastBreathEndTime: Date?
    private var breathEndCandidateTime: Date?
    private var stateConfidence: Float = 0.0
    
    // MARK: - Main Processing Method
    
    func processBreathingSignals(pressure: Float, frequency: Float, audioLevel: Float, timestamp: Date = Date()) {
        
        // 1. Update smoothed values
        updateSmoothingBuffers(pressure: pressure, frequency: frequency, audioLevel: audioLevel)
        
        // 2. Calculate smoothed signals
        let smoothedAudio = calculateSmoothedValue(audioLevelHistory)
        let smoothedPressure = calculateSmoothedValue(pressureHistory)
        let smoothedFrequency = calculateSmoothedValue(frequencyHistory)
        
        // 3. Determine signal strength with hysteresis
        let signalStrength = calculateSignalStrength(
            audio: smoothedAudio,
            pressure: smoothedPressure,
            frequency: smoothedFrequency
        )
        
        // 4. Update confidence based on signal consistency
        updateConfidence(signalStrength: signalStrength)
        
        // 5. Detect breath events with improved logic
        updateBreathDetection(
            signalStrength: signalStrength,
            confidence: stateConfidence,
            timestamp: timestamp
        )
        
        // 6. Update current duration
        updateCurrentDuration(timestamp: timestamp)
        
        // Debug output (can be removed in production)
        if currentState != .idle {
            print("🫁 State: \(currentState), Duration: \(String(format: "%.1f", currentBreathDuration))s, Signal: \(String(format: "%.3f", signalStrength)), Confidence: \(String(format: "%.2f", stateConfidence))")
        }
    }
    
    // MARK: - Smoothing Methods
    
    private func updateSmoothingBuffers(pressure: Float, frequency: Float, audioLevel: Float) {
        // Add new values
        audioLevelHistory.append(audioLevel)
        pressureHistory.append(pressure)
        frequencyHistory.append(frequency)
        
        // Maintain buffer size
        if audioLevelHistory.count > smoothingWindowSize {
            audioLevelHistory.removeFirst()
            pressureHistory.removeFirst()
            frequencyHistory.removeFirst()
        }
    }
    
    private func calculateSmoothedValue(_ history: [Float]) -> Float {
        guard !history.isEmpty else { return 0.0 }
        
        // Weighted average with more weight on recent values
        var weightedSum: Float = 0.0
        var totalWeight: Float = 0.0
        
        for (index, value) in history.enumerated() {
            let weight = Float(index + 1) // More recent = higher weight
            weightedSum += value * weight
            totalWeight += weight
        }
        
        return weightedSum / totalWeight
    }
    
    private func calculateSignalStrength(audio: Float, pressure: Float, frequency: Float) -> Float {
        // Combine multiple signals with weights
        let audioWeight: Float = 0.4
        let pressureWeight: Float = 0.4
        let frequencyWeight: Float = 0.2
        
        // Normalize each signal (0-1 range)
        let normalizedAudio = min(audio / 0.05, 1.0)  // Max expected audio level
        let normalizedPressure = min(pressure / 25.0, 1.0)  // Max expected pressure
        let normalizedFrequency = min(frequency / 50.0, 1.0)  // Max expected frequency
        
        return (normalizedAudio * audioWeight) + 
               (normalizedPressure * pressureWeight) + 
               (normalizedFrequency * frequencyWeight)
    }
    
    private func updateConfidence(signalStrength: Float) {
        confidenceHistory.append(signalStrength)
        
        if confidenceHistory.count > smoothingWindowSize {
            confidenceHistory.removeFirst()
        }
        
        // Confidence based on signal consistency
        if confidenceHistory.count >= 3 {
            let average = confidenceHistory.reduce(0, +) / Float(confidenceHistory.count)
            let variance = confidenceHistory.map { pow($0 - average, 2) }.reduce(0, +) / Float(confidenceHistory.count)
            let stability = max(0, 1.0 - variance * 4.0) // Scale variance
            
            stateConfidence = average * stability
        } else {
            stateConfidence = signalStrength
        }
    }
    
    // MARK: - Improved Breath Detection Logic
    
    private func updateBreathDetection(signalStrength: Float, confidence: Float, timestamp: Date) {
        
        // Track when we last had a significant signal
        if signalStrength > 0.1 && confidence > 0.3 {
            lastSignificantSignalTime = timestamp
            
            // Add to amplitude history for breath quality measurement
            amplitudeHistory.append(signalStrength)
            if amplitudeHistory.count > 100 {
                amplitudeHistory.removeFirst(50)
            }
        }
        
        switch currentState {
        case .idle:
            handleIdleState(signalStrength: signalStrength, confidence: confidence, timestamp: timestamp)
            
        case .starting:
            handleStartingState(signalStrength: signalStrength, confidence: confidence, timestamp: timestamp)
            
        case .active:
            handleActiveState(signalStrength: signalStrength, confidence: confidence, timestamp: timestamp)
            
        case .ending:
            handleEndingState(signalStrength: signalStrength, confidence: confidence, timestamp: timestamp)
            
        case .completed:
            // Brief state, immediately return to idle
            currentState = .idle
            isBreathing = false
        }
    }
    
    private func handleIdleState(signalStrength: Float, confidence: Float, timestamp: Date) {
        // Use hysteresis: higher threshold to start breathing
        let shouldStart = signalStrength > 0.15 && confidence > confidenceThreshold
        
        if shouldStart {
            // Prevent double-counting if too soon after last breath
            if let lastEnd = lastBreathEndTime,
               timestamp.timeIntervalSince(lastEnd) < minimumBreathInterval {
                return
            }
            
            startNewBreath(timestamp: timestamp)
        }
    }
    
    private func handleStartingState(signalStrength: Float, confidence: Float, timestamp: Date) {
        guard let startTime = currentBreathStartTime else { return }
        
        let duration = timestamp.timeIntervalSince(startTime)
        
        // Check if signal is still strong enough
        if signalStrength < 0.05 || confidence < 0.3 {
            checkForEarlyEnd(timestamp: timestamp)
            return
        }
        
        // Promote to active if duration is sufficient
        if duration >= minimumBreathDuration {
            currentState = .active
            isBreathing = true
            print("🫁 Breath promoted to active (duration: \(String(format: "%.1f", duration))s)")
        }
    }
    
    private func handleActiveState(signalStrength: Float, confidence: Float, timestamp: Date) {
        // Use hysteresis: lower threshold to continue breathing
        let shouldContinue = signalStrength > 0.08 || confidence > (confidenceThreshold * 0.6)
        
        if shouldContinue {
            // Reset any pending end
            breathEndCandidateTime = nil
            isBreathing = true
        } else {
            // Start considering end
            if breathEndCandidateTime == nil {
                breathEndCandidateTime = timestamp
                currentState = .ending
                print("🫁 Considering breath end...")
            }
        }
    }
    
    private func handleEndingState(signalStrength: Float, confidence: Float, timestamp: Date) {
        // Check if signal resumed
        if signalStrength > 0.12 && confidence > (confidenceThreshold * 0.75) {
            // Signal resumed, back to active
            currentState = .active
            breathEndCandidateTime = nil
            isBreathing = true
            print("🫁 Breath resumed, back to active")
            return
        }
        
        // Check if enough time has passed to confirm end
        if let candidateTime = breathEndCandidateTime {
            let silenceGap = timestamp.timeIntervalSince(candidateTime)
            
            if silenceGap >= confirmationDelay {
                confirmBreathEnd(timestamp: candidateTime)
            }
        }
    }
    
    private func startNewBreath(timestamp: Date) {
        currentBreathStartTime = timestamp
        amplitudeHistory.removeAll()
        currentState = .starting
        isBreathing = true
        breathEndCandidateTime = nil
        
        print("🫁 Starting new breath detection")
    }
    
    private func checkForEarlyEnd(timestamp: Date) {
        guard let startTime = currentBreathStartTime else { return }
        
        let duration = timestamp.timeIntervalSince(startTime)
        
        // If too short, discard
        if duration < minimumBreathDuration {
            print("🫁 Discarding short breath attempt (duration: \(String(format: "%.1f", duration))s)")
            resetToIdle()
        } else {
            // Long enough, treat as valid
            confirmBreathEnd(timestamp: timestamp)
        }
    }
    
    private func confirmBreathEnd(timestamp: Date) {
        guard let startTime = currentBreathStartTime else { return }
        
        let duration = timestamp.timeIntervalSince(startTime)
        let avgAmplitude = amplitudeHistory.isEmpty ? 0 : amplitudeHistory.reduce(0, +) / Float(amplitudeHistory.count)
        let maxAmplitude = amplitudeHistory.max() ?? 0
        
        // Create breath event
        let breathEvent = BreathEvent(
            breathNumber: breathCount + 1,
            startTime: startTime,
            endTime: timestamp,
            duration: duration,
            averageAmplitude: avgAmplitude,
            maxAmplitude: maxAmplitude
        )
        
        // Only count if meets minimum duration
        if duration >= minimumBreathDuration {
            breathCount += 1
            lastBreathEvent = breathEvent
            print("🫁 ✅ Completed breath #\(breathCount) (duration: \(String(format: "%.1f", duration))s)")
        } else {
            print("🫁 ❌ Discarded short breath (duration: \(String(format: "%.1f", duration))s)")
        }
        
        // Reset state
        currentState = .completed
        lastBreathEndTime = timestamp
        resetToIdle()
        
        // Brief delay before returning to idle
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if self.currentState == .completed {
                self.currentState = .idle
            }
        }
    }
    
    private func resetToIdle() {
        currentBreathStartTime = nil
        lastSignificantSignalTime = nil
        breathEndCandidateTime = nil
        amplitudeHistory.removeAll()
        isBreathing = false
    }
    
    private func updateCurrentDuration(timestamp: Date) {
        if let startTime = currentBreathStartTime {
            currentBreathDuration = timestamp.timeIntervalSince(startTime)
        } else {
            currentBreathDuration = 0
        }
    }
    
    // MARK: - Configuration Management
    
    func updateConfiguration(_ newConfig: BreathDetectionConfig) {
        config = newConfig
        print("🫁 ImprovedBreathDetector configuration updated")
        print("   - Smoothing window: \(smoothingWindowSize)")
        print("   - Confidence threshold: \(confidenceThreshold)")
        print("   - Min breath duration: \(minimumBreathDuration)s")
        print("   - Max silence gap: \(maxSilenceGap)s")
    }
    
    // MARK: - Public Methods
    
    func reset() {
        currentState = .idle
        breathCount = 0
        currentBreathDuration = 0
        isBreathing = false
        lastBreathEvent = nil
        
        // Clear all buffers
        audioLevelHistory.removeAll()
        pressureHistory.removeAll()
        frequencyHistory.removeAll()
        confidenceHistory.removeAll()
        amplitudeHistory.removeAll()
        
        // Reset state
        resetToIdle()
        lastBreathEndTime = nil
        stateConfidence = 0.0
        
        print("🫁 Breath detection reset")
    }
    
    // MARK: - Compatibility Methods (same interface as original)
    
    func processAudioLevel(_ audioLevel: Float, timestamp: Date = Date()) {
        // For backward compatibility, treat as audio-only detection
        processBreathingSignals(pressure: 0, frequency: 0, audioLevel: audioLevel, timestamp: timestamp)
    }
    
    var isInValidBreath: Bool {
        return currentState == .active || (currentState == .starting && currentBreathDuration >= minimumBreathDuration)
    }
    
    var statusDescription: String {
        switch currentState {
        case .idle: return "Ready to breathe"
        case .starting: return "Breath starting..."
        case .active: return "Breathing active"
        case .ending: return "Breath ending..."
        case .completed: return "Breath completed"
        }
    }
    
    var currentBreathNumber: Int { breathCount + 1 }
    var breathCountText: String { "\(breathCount)" }
    var currentDurationText: String { String(format: "%.1f", currentBreathDuration) }
    var lastBreathDurationText: String {
        guard let lastBreath = lastBreathEvent else { return "--" }
        return String(format: "%.1f", lastBreath.duration)
    }
}