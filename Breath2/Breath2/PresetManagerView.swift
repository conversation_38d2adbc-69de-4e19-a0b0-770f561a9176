//
//  PresetManagerView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Development UI for managing and testing algorithm presets

import SwiftUI

struct PresetManagerView: View {
    @ObservedObject var configManager: ConfigurationManager
    @State private var showingCustomPresetSheet = false
    @State private var customPresetName = ""
    @State private var showingComparisonView = false
    @State private var comparisonPresets: [ConfigurationPreset] = []
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                headerSection
                currentPresetSection
                availablePresetsSection
                customPresetsSection
                comparisonSection
            }
            .padding()
        }
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.05, blue: 0.08),
                    Color(red: 0.08, green: 0.08, blue: 0.12),
                    Color(red: 0.10, green: 0.10, blue: 0.15)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .sheet(isPresented: $showingCustomPresetSheet) {
            CustomPresetSheet(
                presetName: $customPresetName,
                configManager: configManager
            )
        }
        .sheet(isPresented: $showingComparisonView) {
            PresetComparisonView(
                presets: comparisonPresets,
                configManager: configManager
            )
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "slider.horizontal.3")
                    .font(.title2)
                    .foregroundColor(.cyan)
                
                Text("Preset Manager")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button("Compare") {
                    comparisonPresets = [configManager.selectedPreset, .standard]
                    showingComparisonView = true
                }
                .buttonStyle(.bordered)
                .foregroundColor(.cyan)
            }
            
            Text("Manage and test different algorithm configurations")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
    }
    
    // MARK: - Current Preset Section
    
    private var currentPresetSection: some View {
        ParameterSection(title: "Current Configuration", icon: "checkmark.circle.fill") {
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(configManager.selectedPreset.rawValue)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.cyan)
                        
                        Text(presetDescription(configManager.selectedPreset))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    if configManager.isCustomConfiguration {
                        Button("Edit") {
                            // Navigate to custom configuration editor
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.orange)
                    }
                }
                
                PresetSummaryCard(
                    configuration: configManager.currentConfiguration,
                    isActive: true
                )
            }
        }
    }
    
    // MARK: - Available Presets Section
    
    private var availablePresetsSection: some View {
        ParameterSection(title: "Available Presets", icon: "list.bullet") {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(ConfigurationPreset.allCases.filter { $0 != .custom }, id: \.self) { preset in
                    DeveloperPresetCard(
                        preset: preset,
                        isSelected: preset == configManager.selectedPreset,
                        onSelect: {
                            configManager.applyPreset(preset)
                        }
                    )
                }
            }
        }
    }
    
    // MARK: - Custom Presets Section
    
    private var customPresetsSection: some View {
        ParameterSection(title: "Custom Presets", icon: "plus.circle") {
            VStack(spacing: 16) {
                HStack {
                    Text("Create and manage custom configurations")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Button("New Custom Preset") {
                        customPresetName = ""
                        showingCustomPresetSheet = true
                    }
                    .buttonStyle(.borderedProminent)
                    .foregroundColor(.white)
                }
                
                // TODO: Add saved custom presets list
                Text("Custom presets will appear here")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
            }
        }
    }
    
    // MARK: - Comparison Section
    
    private var comparisonSection: some View {
        ParameterSection(title: "Quick Actions", icon: "bolt.fill") {
            VStack(spacing: 12) {
                HStack {
                    Button("A/B Test Mode") {
                        // TODO: Implement A/B testing
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.cyan)
                    
                    Spacer()
                    
                    Button("Benchmark All") {
                        // TODO: Implement benchmarking
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.green)
                }
                
                HStack {
                    Button("Export Current") {
                        // Export current configuration
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.orange)
                    
                    Spacer()
                    
                    Button("Reset to Paper") {
                        configManager.applyPreset(.standard)
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.red)
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func presetDescription(_ preset: ConfigurationPreset) -> String {
        switch preset {
        case .paperCompliant:
            return "Strict paper-compliant configuration"
        case .standard:
            return "Balanced settings based on research paper"
        case .realTimeFeedback:
            return "Optimized for low latency real-time feedback"
        case .highAccuracy:
            return "Maximum accuracy with higher CPU usage"
        case .stableReadings:
            return "Smooth, stable output with slower response"
        case .lowPower:
            return "Reduced CPU usage for battery conservation"
        case .noisyEnvironment:
            return "Enhanced noise rejection for clinical settings"
        case .pediatric:
            return "Optimized for pediatric therapy sessions"
        case .elderly:
            return "Adapted for elderly patients and COPD"
        case .athletic:
            return "High-performance settings for athletic training"
        case .research:
            return "Research-grade accuracy with data logging"
        case .custom:
            return "User-customized configuration"
        }
    }
}

// MARK: - Developer Preset Card

struct DeveloperPresetCard: View {
    let preset: ConfigurationPreset
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(preset.rawValue)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.cyan)
                    }
                }
                
                Text(presetDescription(preset))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
                
                Spacer()
            }
            .padding(12)
            .frame(height: 80)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.cyan.opacity(0.1) : Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.cyan : Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func presetDescription(_ preset: ConfigurationPreset) -> String {
        switch preset {
        case .paperCompliant: return "Paper compliant"
        case .standard: return "Balanced research-based settings"
        case .realTimeFeedback: return "Low latency, fast response"
        case .highAccuracy: return "Maximum precision"
        case .stableReadings: return "Smooth, stable output"
        case .lowPower: return "Battery efficient"
        case .noisyEnvironment: return "Noise rejection"
        case .pediatric: return "Pediatric optimized"
        case .elderly: return "COPD/elderly adapted"
        case .athletic: return "High performance"
        case .research: return "Research grade"
        case .custom: return "User customized"
        }
    }
}

// MARK: - Preset Summary Card

struct PresetSummaryCard: View {
    let configuration: AlgorithmConfiguration
    let isActive: Bool
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Sample Rate")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(Int(configuration.sampleRate)) Hz")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Buffer Size")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(Int(configuration.bufferSize * 1000)) ms")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Correlation")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(String(format: "%.2f", configuration.correlationThreshold))
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Freq Range")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(Int(configuration.minFreq))-\(Int(configuration.maxFreq)) Hz")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isActive ? Color.cyan.opacity(0.1) : Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(isActive ? Color.cyan.opacity(0.3) : Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

// MARK: - Custom Preset Sheet

struct CustomPresetSheet: View {
    @Binding var presetName: String
    @ObservedObject var configManager: ConfigurationManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Create Custom Preset")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .padding()
                
                TextField("Preset Name", text: $presetName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .padding(.horizontal)
                
                Text("This will save your current configuration as a custom preset")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                Spacer()
            }
            .navigationTitle("Custom Preset")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        // TODO: Implement custom preset saving
                        dismiss()
                    }
                    .disabled(presetName.isEmpty)
                }
            }
        }
    }
}

// MARK: - Preset Comparison View

struct PresetComparisonView: View {
    let presets: [ConfigurationPreset]
    @ObservedObject var configManager: ConfigurationManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Preset Comparison")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .padding()
                    
                    // TODO: Implement detailed preset comparison
                    Text("Comparison view coming soon")
                        .foregroundColor(.secondary)
                }
                .padding()
            }
            .navigationTitle("Compare Presets")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    PresetManagerView(configManager: ConfigurationManager())
}
