//
//  HistoryIntegrationTest.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// Test view to verify the history integration works correctly
struct HistoryIntegrationTest: View {
    @StateObject private var historyManager = SessionHistoryManager()
    @State private var showingModernHistory = false

    var body: some View {
        let dataManager = HistoryDataManager(sessionHistoryManager: historyManager)

        NavigationView {
            VStack(spacing: 20) {
                Text("History Integration Test")
                    .font(.title)
                    .padding()

                <PERSON><PERSON>("Test Modern History View") {
                    showingModernHistory = true
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
                
                // Test individual components
                VStack(spacing: 16) {
                    Text("Component Tests")
                        .font(.headline)
                    
                    // Test progress ring
                    AnimatedProgressRing(
                        progress: 0.75,
                        value: "75%",
                        subtitle: "Test Progress"
                    )
                    .frame(width: 120, height: 120)
                    
                    // Test metric card
                    MagicMetricCard(
                        title: "Test Metric",
                        value: "42",
                        subtitle: "Test subtitle",
                        icon: "star.fill",
                        color: .blue
                    )
                    .frame(height: 120)
                }
                .padding()
                
                Spacer()
            }
            .background(
                LinearGradient(
                    colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),
                        Color(red: 0.08, green: 0.08, blue: 0.12)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationTitle("Integration Test")
            .sheet(isPresented: $showingModernHistory) {
                ModernHistoryView(historyDataManager: dataManager)
            }
        }
    }
}

#Preview {
    HistoryIntegrationTest()
}
