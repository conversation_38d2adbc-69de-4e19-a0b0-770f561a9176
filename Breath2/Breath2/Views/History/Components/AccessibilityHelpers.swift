//
//  AccessibilityHelpers.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// Accessibility helpers and performance optimizations for the history section
struct AccessibilityHelpers {
    
    /// Accessible progress ring with VoiceOver support
    struct AccessibleProgressRing: View {
        let progress: Double
        let value: String
        let subtitle: String
        let accessibilityLabel: String
        let accessibilityValue: String
        
        var body: some View {
            AnimatedProgressRing(
                progress: progress,
                value: value,
                subtitle: subtitle
            )
            .accessibilityElement(children: .ignore)
            .accessibilityLabel(accessibilityLabel)
            .accessibilityValue(accessibilityValue)
            .accessibilityAddTraits(.updatesFrequently)
            .accessibilityHint("Double tap to view detailed progress information")
        }
    }
    
    /// Accessible metric card with proper labeling
    struct AccessibleMetricCard: View {
        let title: String
        let value: String
        let subtitle: String?
        let icon: String
        let color: Color
        let trend: MagicMetricCard.TrendDirection?
        let trendValue: String?
        let onTap: (() -> Void)?
        
        var body: some View {
            Button(action: onTap ?? {}) {
                MagicMetricCard(
                    title: title,
                    value: value,
                    subtitle: subtitle,
                    icon: icon,
                    color: color,
                    trend: trend,
                    trendValue: trendValue
                )
            }
            .buttonStyle(PlainButtonStyle())
            .accessibilityElement(children: .ignore)
            .accessibilityLabel(accessibilityLabel)
            .accessibilityValue(accessibilityValue)
            .accessibilityHint(accessibilityHint)
            .accessibilityAddTraits(onTap != nil ? [.isButton] : [])
        }
        
        private var accessibilityLabel: String {
            var label = title
            if let subtitle = subtitle {
                label += ", \(subtitle)"
            }
            return label
        }
        
        private var accessibilityValue: String {
            var valueText = value
            if let trend = trend, let trendValue = trendValue {
                let trendDescription = trend == .up ? "increasing" : trend == .down ? "decreasing" : "stable"
                valueText += ", trend \(trendDescription) by \(trendValue)"
            }
            return valueText
        }
        
        private var accessibilityHint: String {
            if onTap != nil {
                return "Double tap to view detailed information"
            } else {
                return "Metric information"
            }
        }
    }
    
    /// Accessible session list item
    struct AccessibleSessionItem: View {
        let session: Session
        let onTap: () -> Void
        
        var body: some View {
            Button(action: onTap) {
                SessionListItem(session: session, onTap: onTap)
            }
            .buttonStyle(PlainButtonStyle())
            .accessibilityElement(children: .ignore)
            .accessibilityLabel(accessibilityLabel)
            .accessibilityValue(accessibilityValue)
            .accessibilityHint("Double tap to view session details")
            .accessibilityAddTraits(.isButton)
        }
        
        private var accessibilityLabel: String {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            
            return "Therapy session from \(formatter.string(from: session.startTime))"
        }
        
        private var accessibilityValue: String {
            let duration = Int(session.duration / 60)
            let quality = session.quality.description
            let completion = session.isCompleted ? "completed" : "incomplete"
            
            return "\(duration) minutes, \(quality) quality, \(completion), \(session.breathCount) breaths"
        }
    }
    
    /// Accessible chart with data table fallback
    struct AccessibleChart: View {
        let dataPoints: [TrendDataPoint]
        let title: String
        let color: Color
        let accessibilityDescription: String
        
        @Environment(\.accessibilityReduceMotion) var reduceMotion
        @Environment(\.accessibilityDifferentiateWithoutColor) var differentiateWithoutColor
        
        var body: some View {
            VStack(alignment: .leading, spacing: 16) {
                TrendChart(
                    dataPoints: dataPoints,
                    title: title,
                    color: differentiateWithoutColor ? .primary : color,
                    showGradient: !reduceMotion
                )
                .accessibilityElement(children: .ignore)
                .accessibilityLabel("\(title) chart")
                .accessibilityValue(accessibilityDescription)
                .accessibilityHint("Chart showing trend data over time")
                
                // Data table for screen readers
                if !dataPoints.isEmpty {
                    AccessibilityDataTable(dataPoints: dataPoints, title: title)
                        .accessibilityHidden(false)
                }
            }
        }
    }
    
    /// Data table for accessibility
    struct AccessibilityDataTable: View {
        let dataPoints: [TrendDataPoint]
        let title: String
        
        var body: some View {
            VStack(alignment: .leading, spacing: 8) {
                Text("Data for \(title)")
                    .font(.headline)
                    .accessibilityAddTraits(.isHeader)
                
                ForEach(Array(dataPoints.enumerated()), id: \.offset) { index, point in
                    HStack {
                        Text(formatDate(point.date))
                            .font(.caption)
                        
                        Spacer()
                        
                        Text("\(Int(point.value))")
                            .font(.caption)
                            .fontWeight(.semibold)
                    }
                    .accessibilityElement(children: .combine)
                    .accessibilityLabel("\(formatDate(point.date)): \(Int(point.value))")
                }
            }
            .padding()
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(8)
            .accessibilityElement(children: .contain)
            .accessibilityLabel("Data table for \(title)")
        }
        
        private func formatDate(_ date: Date) -> String {
            let formatter = DateFormatter()
            formatter.dateStyle = .short
            return formatter.string(from: date)
        }
    }
    
    /// Performance-optimized lazy loading container
    struct LazyLoadingContainer<Content: View>: View {
        let content: () -> Content
        @State private var isVisible = false
        
        init(@ViewBuilder content: @escaping () -> Content) {
            self.content = content
        }
        
        var body: some View {
            GeometryReader { geometry in
                if isVisible {
                    content()
                        .transition(.opacity)
                } else {
                    LoadingSkeleton()
                        .onAppear {
                            // Load content when view appears
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    isVisible = true
                                }
                            }
                        }
                }
            }
        }
    }
    
    /// Reduced motion alternative for animations
    struct ReducedMotionWrapper<Content: View, Alternative: View>: View {
        let content: Content
        let reducedMotionContent: Alternative?

        @Environment(\.accessibilityReduceMotion) var reduceMotion

        init(
            @ViewBuilder content: () -> Content,
            reducedMotionContent: (() -> Alternative)? = nil
        ) {
            self.content = content()
            self.reducedMotionContent = reducedMotionContent?()
        }

        var body: some View {
            if reduceMotion {
                if let alternative = reducedMotionContent {
                    AnyView(alternative)
                } else {
                    AnyView(content)
                }
            } else {
                AnyView(content)
            }
        }
    }
    
    /// High contrast color helper
    struct HighContrastColor {
        static func adaptiveColor(
            light: Color,
            dark: Color,
            highContrast: Color
        ) -> Color {
            // This would typically use @Environment(\.accessibilityIncreaseContrast)
            // For now, return the standard colors
            return light
        }
        
        static let adaptivePrimary = Color.primary
        static let adaptiveSecondary = Color.secondary
        static let adaptiveBackground = Color(UIColor.systemBackground)
        static let adaptiveGroupedBackground = Color(UIColor.systemGroupedBackground)
    }
    
    /// Voice Control optimized button
    struct VoiceControlButton<Content: View>: View {
        let content: Content
        let action: () -> Void
        let voiceControlLabel: String
        
        init(
            voiceControlLabel: String,
            action: @escaping () -> Void,
            @ViewBuilder content: () -> Content
        ) {
            self.voiceControlLabel = voiceControlLabel
            self.action = action
            self.content = content()
        }
        
        var body: some View {
            Button(action: action) {
                content
            }
            .accessibilityInputLabels([voiceControlLabel])
            .accessibilityLabel(voiceControlLabel)
        }
    }
}

// MARK: - Performance Optimizations

/// Memory-efficient image cache for session thumbnails
class ImageCache: ObservableObject {
    private let cache = NSCache<NSString, UIImage>()
    private let maxCacheSize = 50 // Maximum number of cached images
    
    init() {
        cache.countLimit = maxCacheSize
    }
    
    func image(for key: String) -> UIImage? {
        return cache.object(forKey: NSString(string: key))
    }
    
    func setImage(_ image: UIImage, for key: String) {
        cache.setObject(image, forKey: NSString(string: key))
    }
    
    func clearCache() {
        cache.removeAllObjects()
    }
}

/// Debounced search helper
class DebouncedSearch: ObservableObject {
    @Published var searchText = ""
    @Published var debouncedSearchText = ""
    
    private var searchTask: Task<Void, Never>?
    private let delay: TimeInterval
    
    init(delay: TimeInterval = 0.5) {
        self.delay = delay
        
        // Set up debouncing
        Task {
            for await searchText in $searchText.values {
                searchTask?.cancel()
                searchTask = Task {
                    try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                    if !Task.isCancelled {
                        await MainActor.run {
                            self.debouncedSearchText = searchText
                        }
                    }
                }
            }
        }
    }
}

// MARK: - View Extensions for Accessibility

extension View {
    /// Apply accessibility optimizations
    func accessibilityOptimized() -> some View {
        self
            .accessibilityElement(children: .contain)
    }
    
    /// Apply performance optimizations
    func performanceOptimized() -> some View {
        self
            .drawingGroup() // Flatten view hierarchy for better performance
            .clipped() // Prevent overdraw
    }
    
    /// Apply reduced motion alternatives
    func reducedMotionAlternative<T: View>(@ViewBuilder alternative: @escaping () -> T) -> some View {
        AccessibilityHelpers.ReducedMotionWrapper(
            content: { self },
            reducedMotionContent: alternative
        )
    }
    
    /// Apply high contrast adaptations
    func highContrastAdaptive() -> some View {
        self
            .environment(\.colorScheme, .dark) // Ensure proper contrast
    }
}

// MARK: - Accessibility Constants

struct AccessibilityConstants {
    static let minimumTouchTarget: CGFloat = 44
    static let preferredTouchTarget: CGFloat = 48
    static let maximumAnimationDuration: TimeInterval = 0.3
    static let minimumContrastRatio: Double = 4.5
    
    // VoiceOver labels
    struct Labels {
        static let sessionQuality = "Session quality indicator"
        static let adherenceRate = "Therapy adherence rate"
        static let progressRing = "Progress completion ring"
        static let trendChart = "Progress trend chart"
        static let sessionList = "Session history list"
        static let filterButton = "Filter sessions"
        static let searchField = "Search sessions"
    }
    
    // VoiceOver hints
    struct Hints {
        static let tapForDetails = "Double tap to view detailed information"
        static let swipeForMore = "Swipe up or down to see more options"
        static let adjustable = "Swipe up or down to adjust value"
    }
}

#Preview {
    VStack(spacing: 20) {
        AccessibilityHelpers.AccessibleProgressRing(
            progress: 0.75,
            value: "75%",
            subtitle: "This Week",
            accessibilityLabel: "Weekly progress",
            accessibilityValue: "75 percent complete"
        )
        
        AccessibilityHelpers.AccessibleMetricCard(
            title: "Sessions Completed",
            value: "12/14",
            subtitle: "This month",
            icon: "checkmark.circle.fill",
            color: .green,
            trend: .up,
            trendValue: "+15%",
            onTap: {}
        )
    }
    .padding()
    .background(Color.black)
}
