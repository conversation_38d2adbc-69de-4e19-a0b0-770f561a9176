//
//  TrendChart.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI
import Charts

/// Magic UI-inspired trend chart for displaying session progress over time
struct TrendChart: View {
    let dataPoints: [TrendDataPoint]
    let title: String
    let color: Color
    let showGradient: Bool
    let timeframe: TrendTimeframe
    
    @State private var animateChart = false
    @State private var selectedPoint: TrendDataPoint?
    
    enum TrendTimeframe: String, CaseIterable {
        case week = "7D"
        case month = "30D"
        case quarter = "3M"
        case year = "1Y"
        
        var displayName: String {
            switch self {
            case .week: return "This Week"
            case .month: return "This Month"
            case .quarter: return "3 Months"
            case .year: return "This Year"
            }
        }
    }
    
    init(
        dataPoints: [TrendDataPoint],
        title: String,
        color: Color = .cyan,
        showGradient: Bool = true,
        timeframe: TrendTimeframe = .week
    ) {
        self.dataPoints = dataPoints
        self.title = title
        self.color = color
        self.showGradient = showGradient
        self.timeframe = timeframe
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            headerView
            chartContentView
        }
        .padding(20)
        .background(chartBackground)
    }

    // MARK: - Header View
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))

                Text(timeframe.displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
            }

            Spacer()

            trendIndicatorView
        }
    }

    // MARK: - Trend Indicator
    private var trendIndicatorView: some View {
        Group {
            if let trendValue = calculateTrend() {
                let trendIcon = getTrendIcon(for: trendValue)
                let trendColor = getTrendColor(for: trendValue)

                HStack(spacing: 4) {
                    Image(systemName: trendIcon)
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(trendColor)

                    Text("\(abs(Int(trendValue)))%")
                        .font(.system(size: 12, weight: .semibold, design: .rounded))
                        .foregroundColor(trendColor)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(trendIndicatorBackground(color: trendColor))
            }
        }
    }

    // MARK: - Chart Content
    private var chartContentView: some View {
        Group {
            if !dataPoints.isEmpty {
                VStack(spacing: 16) {
                    chartView
                    selectedPointDetailsView
                }
            } else {
                emptyStateView
            }
        }
    }

    // MARK: - Chart View
    private var chartView: some View {
        let animatedValue: (Double) -> Double = { value in
            return animateChart ? value : 0
        }

        return Chart(dataPoints) { point in
            // Area gradient
            if showGradient {
                AreaMark(
                    x: .value("Date", point.date),
                    y: .value("Value", animatedValue(point.value))
                )
                .foregroundStyle(areaGradient)
            }

            // Line
            LineMark(
                x: .value("Date", point.date),
                y: .value("Value", animatedValue(point.value))
            )
            .foregroundStyle(color)
            .lineStyle(StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round))

            // Points
            let pointValue = animatedValue(point.value)
            let isSelected = selectedPoint?.id == point.id
            let symbolSize: CGFloat = isSelected ? 100 : 50
            let pointOpacity: Double = isSelected ? 1.0 : 0.8

            PointMark(
                x: .value("Date", point.date),
                y: .value("Value", pointValue)
            )
            .foregroundStyle(color)
            .symbolSize(symbolSize)
            .opacity(pointOpacity)
        }
        .frame(height: 200)
        .chartBackground { chartProxy in
            chartBackgroundGrid
        }
        .chartXAxis {
            let strideCount = timeframe == .week ? 1 : 7
            AxisMarks(values: .stride(by: .day, count: strideCount)) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0))
                AxisTick(stroke: StrokeStyle(lineWidth: 0))
                AxisValueLabel {
                    if let date = value.as(Date.self) {
                        Text(formatAxisDate(date))
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.white.opacity(0.6))
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(position: .trailing) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 0))
                AxisTick(stroke: StrokeStyle(lineWidth: 0))
                AxisValueLabel {
                    if let intValue = value.as(Double.self) {
                        Text("\(Int(intValue))")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.white.opacity(0.6))
                    }
                }
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.5)) {
                animateChart = true
            }
        }
    }

    // MARK: - Chart Background Grid
    private var chartBackgroundGrid: some View {
        Rectangle()
            .fill(Color.clear)
            .overlay(
                VStack {
                    ForEach(0..<5) { i in
                        Rectangle()
                            .fill(Color.white.opacity(0.05))
                            .frame(height: 1)
                        if i < 4 { Spacer() }
                    }
                }
            )
    }

    // MARK: - Selected Point Details
    private var selectedPointDetailsView: some View {
        Group {
            if let selectedPoint = selectedPoint {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(formatSelectedDate(selectedPoint.date))
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white.opacity(0.6))

                        Text("\(Int(selectedPoint.value))")
                            .font(.system(size: 16, weight: .bold, design: .rounded))
                            .foregroundColor(color)
                    }

                    Spacer()
                }
                .padding(12)
                .background(selectedPointBackground)
                .transition(.opacity.combined(with: .scale))
            }
        }
    }

    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "chart.line.uptrend.xyaxis")
                .font(.system(size: 32, weight: .light))
                .foregroundColor(.white.opacity(0.4))

            Text("No data available")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.6))
        }
        .frame(height: 200)
        .frame(maxWidth: .infinity)
    }

    // MARK: - Helper Methods and Computed Properties

    private func getTrendIcon(for value: Double) -> String {
        if value > 0 {
            return "arrow.up.right"
        } else if value < 0 {
            return "arrow.down.right"
        } else {
            return "minus"
        }
    }

    private func getTrendColor(for value: Double) -> Color {
        if value > 0 {
            return .green
        } else if value < 0 {
            return .red
        } else {
            return .gray
        }
    }

    private func trendIndicatorBackground(color: Color) -> some View {
        Capsule()
            .fill(color.opacity(0.1))
            .overlay(
                Capsule()
                    .stroke(color.opacity(0.3), lineWidth: 1)
            )
    }

    private var areaGradient: LinearGradient {
        LinearGradient(
            colors: [
                color.opacity(0.3),
                color.opacity(0.1),
                Color.clear
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    }

    private var selectedPointBackground: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color.white.opacity(0.05))
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(color.opacity(0.3), lineWidth: 1)
            )
    }

    private var chartBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(
                LinearGradient(
                    colors: [
                        Color.white.opacity(0.05),
                        Color.white.opacity(0.02)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(
                        LinearGradient(
                            colors: [
                                color.opacity(0.2),
                                Color.white.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .shadow(
                color: color.opacity(0.1),
                radius: 10,
                x: 0,
                y: 5
            )
    }

    private func calculateTrend() -> Double? {
        guard dataPoints.count >= 2 else { return nil }
        
        let recent = dataPoints.suffix(3).map(\.value)
        let previous = dataPoints.prefix(dataPoints.count - 3).suffix(3).map(\.value)
        
        guard !recent.isEmpty && !previous.isEmpty else { return nil }
        
        let recentAvg = recent.reduce(0, +) / Double(recent.count)
        let previousAvg = previous.reduce(0, +) / Double(previous.count)
        
        guard previousAvg > 0 else { return nil }
        
        return ((recentAvg - previousAvg) / previousAvg) * 100
    }
    
    private func formatAxisDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        switch timeframe {
        case .week:
            formatter.dateFormat = "E"
        case .month:
            formatter.dateFormat = "d"
        case .quarter, .year:
            formatter.dateFormat = "MMM"
        }
        return formatter.string(from: date)
    }
    
    private func formatSelectedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }
}

/// Data point for trend charts
struct TrendDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let value: Double
}

#Preview {
    ScrollView {
        VStack(spacing: 20) {
            TrendChart(
                dataPoints: [
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -6, to: Date())!, value: 12),
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -5, to: Date())!, value: 15),
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -4, to: Date())!, value: 18),
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -3, to: Date())!, value: 14),
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -2, to: Date())!, value: 20),
                    TrendDataPoint(date: Calendar.current.date(byAdding: .day, value: -1, to: Date())!, value: 22),
                    TrendDataPoint(date: Date(), value: 25)
                ],
                title: "Session Quality",
                color: .cyan
            )
            
            TrendChart(
                dataPoints: [],
                title: "Weekly Progress",
                color: .green
            )
        }
        .padding()
    }
    .background(
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    )
}
