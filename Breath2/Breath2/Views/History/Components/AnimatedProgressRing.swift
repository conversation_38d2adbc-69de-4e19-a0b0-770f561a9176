//
//  AnimatedProgressRing.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// Magic UI-inspired animated circular progress ring for displaying completion rates and metrics
struct AnimatedProgressRing: View {
    let progress: Double
    let value: String
    let subtitle: String
    let size: CGFloat
    let lineWidth: CGFloat
    let colors: [Color]
    let showPercentage: Bool
    
    @State private var animatedProgress: Double = 0
    @State private var isAnimating = false
    
    init(
        progress: Double,
        value: String,
        subtitle: String,
        size: CGFloat = 120,
        lineWidth: CGFloat = 8,
        colors: [Color] = [.cyan, .blue],
        showPercentage: Bool = true
    ) {
        self.progress = min(max(progress, 0), 1)
        self.value = value
        self.subtitle = subtitle
        self.size = size
        self.lineWidth = lineWidth
        self.colors = colors
        self.showPercentage = showPercentage
    }
    
    var body: some View {
        ZStack {
            // Background ring with subtle glow
            Circle()
                .stroke(
                    Color.white.opacity(0.1),
                    lineWidth: lineWidth
                )
                .frame(width: size, height: size)
                .overlay(
                    Circle()
                        .stroke(
                            Color.white.opacity(0.05),
                            lineWidth: lineWidth * 2
                        )
                        .blur(radius: 4)
                )
            
            // Animated progress ring
            Circle()
                .trim(from: 0, to: animatedProgress)
                .stroke(
                    progressGradient,
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round
                    )
                )
                .frame(width: size, height: size)
                .rotationEffect(.degrees(-90))
                .shadow(
                    color: colors.first?.opacity(0.3) ?? .clear,
                    radius: 8,
                    x: 0,
                    y: 0
                )
            
            // Animated glow effect
            if isAnimating {
                Circle()
                    .trim(from: max(0, animatedProgress - 0.1), to: animatedProgress)
                    .stroke(
                        Color.white.opacity(0.8),
                        style: StrokeStyle(
                            lineWidth: lineWidth / 2,
                            lineCap: .round
                        )
                    )
                    .frame(width: size, height: size)
                    .rotationEffect(.degrees(-90))
                    .blur(radius: 2)
            }
            
            // Center content
            VStack(spacing: 4) {
                // Main value with number ticker animation
                Text(value)
                    .font(.system(size: size * 0.2, weight: .bold, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: colors,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .contentTransition(.numericText())
                
                // Percentage indicator
                if showPercentage {
                    Text("\(Int(progress * 100))%")
                        .font(.system(size: size * 0.1, weight: .medium, design: .rounded))
                        .foregroundColor(.white.opacity(0.8))
                        .contentTransition(.numericText())
                }
                
                // Subtitle
                Text(subtitle)
                    .font(.system(size: size * 0.08, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
        }
        .onAppear {
            startAnimation()
        }
        .onChange(of: progress) { _, _ in
            startAnimation()
        }
    }
    
    private var progressGradient: AngularGradient {
        AngularGradient(
            gradient: Gradient(colors: colors + [colors.first ?? .clear]),
            center: .center,
            startAngle: .degrees(0),
            endAngle: .degrees(360 * animatedProgress)
        )
    }
    
    private func startAnimation() {
        withAnimation(.easeInOut(duration: 1.2).delay(0.2)) {
            animatedProgress = progress
        }
        
        withAnimation(.easeInOut(duration: 0.8).delay(0.4)) {
            isAnimating = true
        }
        
        // Stop glow animation after completion
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeOut(duration: 0.5)) {
                isAnimating = false
            }
        }
    }
}

/// Specialized progress ring for session quality display
struct QualityProgressRing: View {
    let quality: SessionQuality
    let sessionsCount: Int
    let totalSessions: Int
    
    var body: some View {
        AnimatedProgressRing(
            progress: totalSessions > 0 ? Double(sessionsCount) / Double(totalSessions) : 0,
            value: "\(sessionsCount)",
            subtitle: quality.description,
            colors: qualityColors,
            showPercentage: false
        )
    }
    
    private var qualityColors: [Color] {
        switch quality {
        case .excellent:
            return [.green, .mint]
        case .good:
            return [.blue, .cyan]
        case .fair:
            return [.orange, .yellow]
        case .poor:
            return [.red, .pink]
        case .none:
            return [.gray, .secondary]
        }
    }
}

/// Weekly adherence progress ring with streak indicator
struct WeeklyAdherenceRing: View {
    let completedSessions: Int
    let plannedSessions: Int
    let currentStreak: Int
    
    var adherenceRate: Double {
        guard plannedSessions > 0 else { return 0 }
        return Double(completedSessions) / Double(plannedSessions)
    }
    
    var body: some View {
        VStack(spacing: 16) {
            AnimatedProgressRing(
                progress: adherenceRate,
                value: "\(completedSessions)",
                subtitle: "This Week",
                size: 140,
                lineWidth: 10,
                colors: adherenceColors
            )
            
            // Streak indicator with sparkle effect
            if currentStreak > 0 {
                HStack(spacing: 6) {
                    Image(systemName: "flame.fill")
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.orange, .red],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .scaleEffect(currentStreak > 7 ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.6).repeatForever(autoreverses: true), value: currentStreak > 7)
                    
                    Text("\(currentStreak) day streak")
                        .font(.system(size: 14, weight: .semibold, design: .rounded))
                        .foregroundColor(.white.opacity(0.9))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    Capsule()
                        .fill(Color.orange.opacity(0.2))
                        .overlay(
                            Capsule()
                                .stroke(Color.orange.opacity(0.4), lineWidth: 1)
                        )
                )
            }
        }
    }
    
    private var adherenceColors: [Color] {
        if adherenceRate >= 0.8 {
            return [.green, .mint]
        } else if adherenceRate >= 0.6 {
            return [.yellow, .orange]
        } else {
            return [.red, .pink]
        }
    }
}

#Preview {
    VStack(spacing: 40) {
        AnimatedProgressRing(
            progress: 0.73,
            value: "73",
            subtitle: "Sessions\nCompleted"
        )
        
        QualityProgressRing(
            quality: .excellent,
            sessionsCount: 12,
            totalSessions: 15
        )
        
        WeeklyAdherenceRing(
            completedSessions: 5,
            plannedSessions: 7,
            currentStreak: 12
        )
    }
    .padding()
    .background(
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    )
}
