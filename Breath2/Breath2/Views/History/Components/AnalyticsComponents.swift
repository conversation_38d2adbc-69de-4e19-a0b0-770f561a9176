//
//  AnalyticsComponents.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI
import Charts

/// Quality analytics view with detailed quality breakdown
struct QualityAnalyticsView: View {
    let data: [AnalyticsDataPoint]
    let timeframe: AnalyticsView.AnalyticsTimeframe
    
    var body: some View {
        VStack(spacing: 20) {
            // Quality trend chart
            TrendChart(
                dataPoints: data.map { TrendDataPoint(date: $0.date, value: $0.value) },
                title: "Session Quality Trend",
                color: .cyan,
                timeframe: TrendChart.TrendTimeframe(rawValue: timeframe.rawValue.uppercased()) ?? .week
            )
            
            // Quality distribution
            QualityDistributionView()
        }
    }
}

/// Adherence analytics view
struct AdherenceAnalyticsView: View {
    let data: [AnalyticsDataPoint]
    let timeframe: AnalyticsView.AnalyticsTimeframe
    
    var body: some View {
        VStack(spacing: 20) {
            // Adherence trend chart
            TrendChart(
                dataPoints: data.map { TrendDataPoint(date: $0.date, value: $0.value) },
                title: "Adherence Rate",
                color: .green,
                timeframe: TrendChart.TrendTimeframe(rawValue: timeframe.rawValue.uppercased()) ?? .week
            )
            
            // Weekly adherence breakdown
            WeeklyAdherenceBreakdown()
        }
    }
}

/// Pressure analytics view
struct PressureAnalyticsView: View {
    let data: [AnalyticsDataPoint]
    let timeframe: AnalyticsView.AnalyticsTimeframe
    
    var body: some View {
        VStack(spacing: 20) {
            // Pressure trend chart
            TrendChart(
                dataPoints: data.map { TrendDataPoint(date: $0.date, value: $0.value) },
                title: "Average Pressure (cmH₂O)",
                color: .orange,
                timeframe: TrendChart.TrendTimeframe(rawValue: timeframe.rawValue.uppercased()) ?? .week
            )
            
            // Pressure zone distribution
            PressureZoneDistribution()
        }
    }
}

/// Duration analytics view
struct DurationAnalyticsView: View {
    let data: [AnalyticsDataPoint]
    let timeframe: AnalyticsView.AnalyticsTimeframe
    
    var body: some View {
        VStack(spacing: 20) {
            // Duration trend chart
            TrendChart(
                dataPoints: data.map { TrendDataPoint(date: $0.date, value: $0.value) },
                title: "Average Session Duration (minutes)",
                color: .purple,
                timeframe: TrendChart.TrendTimeframe(rawValue: timeframe.rawValue.uppercased()) ?? .week
            )
            
            // Duration distribution
            DurationDistribution()
        }
    }
}

/// Patterns analytics view
struct PatternsAnalyticsView: View {
    let data: [AnalyticsDataPoint]
    let timeframe: AnalyticsView.AnalyticsTimeframe
    
    var body: some View {
        VStack(spacing: 20) {
            // Time of day patterns
            TimeOfDayPatterns()
            
            // Day of week patterns
            DayOfWeekPatterns()
        }
    }
}

/// Quality distribution pie chart
struct QualityDistributionView: View {
    let qualityData = [
        QualityData(quality: "Excellent", count: 45, color: .green),
        QualityData(quality: "Good", count: 32, color: .blue),
        QualityData(quality: "Fair", count: 18, color: .orange),
        QualityData(quality: "Poor", count: 5, color: .red)
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quality Distribution")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white.opacity(0.9))
            
            HStack(spacing: 20) {
                // Pie chart representation using progress rings
                ZStack {
                    ForEach(Array(qualityData.enumerated()), id: \.offset) { index, data in
                        Circle()
                            .trim(from: startAngle(for: index), to: endAngle(for: index))
                            .stroke(
                                data.color,
                                style: StrokeStyle(lineWidth: 20, lineCap: .round)
                            )
                            .frame(width: 120, height: 120)
                            .rotationEffect(.degrees(-90))
                    }
                    
                    VStack(spacing: 4) {
                        Text("\(totalSessions)")
                            .font(.system(size: 20, weight: .bold, design: .rounded))
                            .foregroundColor(.white.opacity(0.9))
                        
                        Text("sessions")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white.opacity(0.6))
                    }
                }
                
                // Legend
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(qualityData, id: \.quality) { data in
                        HStack(spacing: 8) {
                            Circle()
                                .fill(data.color)
                                .frame(width: 12, height: 12)
                            
                            Text(data.quality)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white.opacity(0.8))
                            
                            Spacer()
                            
                            Text("\(data.count)")
                                .font(.system(size: 14, weight: .semibold, design: .rounded))
                                .foregroundColor(data.color)
                        }
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    private var totalSessions: Int {
        qualityData.reduce(0) { $0 + $1.count }
    }
    
    private func startAngle(for index: Int) -> Double {
        let previousCounts = qualityData.prefix(index).reduce(0) { $0 + $1.count }
        return Double(previousCounts) / Double(totalSessions)
    }
    
    private func endAngle(for index: Int) -> Double {
        let previousCounts = qualityData.prefix(index + 1).reduce(0) { $0 + $1.count }
        return Double(previousCounts) / Double(totalSessions)
    }
}

/// Weekly adherence breakdown
struct WeeklyAdherenceBreakdown: View {
    let weeklyData = [
        WeeklyAdherence(week: "This Week", completed: 6, planned: 7, color: .green),
        WeeklyAdherence(week: "Last Week", completed: 7, planned: 7, color: .green),
        WeeklyAdherence(week: "2 Weeks Ago", completed: 5, planned: 7, color: .orange),
        WeeklyAdherence(week: "3 Weeks Ago", completed: 4, planned: 7, color: .red)
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Weekly Breakdown")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white.opacity(0.9))
            
            VStack(spacing: 12) {
                ForEach(weeklyData, id: \.week) { data in
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(data.week)
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.white.opacity(0.9))
                            
                            Text("\(data.completed)/\(data.planned) sessions")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white.opacity(0.6))
                        }
                        
                        Spacer()
                        
                        // Progress bar
                        GeometryReader { geometry in
                            ZStack(alignment: .leading) {
                                Rectangle()
                                    .fill(Color.white.opacity(0.1))
                                    .frame(height: 6)
                                    .cornerRadius(3)
                                
                                Rectangle()
                                    .fill(data.color)
                                    .frame(width: geometry.size.width * data.adherenceRate, height: 6)
                                    .cornerRadius(3)
                                    .animation(.easeInOut(duration: 1.0), value: data.adherenceRate)
                            }
                        }
                        .frame(width: 80, height: 6)
                        
                        Text("\(Int(data.adherenceRate * 100))%")
                            .font(.system(size: 14, weight: .semibold, design: .rounded))
                            .foregroundColor(data.color)
                            .frame(width: 40, alignment: .trailing)
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

/// Pressure zone distribution
struct PressureZoneDistribution: View {
    let zoneData = [
        AnalyticsPressureZone(zone: "Green Zone", percentage: 65, color: .green, description: "Optimal pressure"),
        AnalyticsPressureZone(zone: "Amber Zone", percentage: 25, color: .orange, description: "Acceptable pressure"),
        AnalyticsPressureZone(zone: "Red Zone", percentage: 10, color: .red, description: "Too high/low")
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Pressure Zone Distribution")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white.opacity(0.9))
            
            VStack(spacing: 16) {
                ForEach(zoneData, id: \.zone) { data in
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text(data.zone)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(data.color)
                            
                            Spacer()
                            
                            Text("\(data.percentage)%")
                                .font(.system(size: 16, weight: .bold, design: .rounded))
                                .foregroundColor(data.color)
                        }
                        
                        Text(data.description)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.6))
                        
                        // Progress bar
                        GeometryReader { geometry in
                            ZStack(alignment: .leading) {
                                Rectangle()
                                    .fill(Color.white.opacity(0.1))
                                    .frame(height: 8)
                                    .cornerRadius(4)
                                
                                Rectangle()
                                    .fill(
                                        LinearGradient(
                                            colors: [data.color, data.color.opacity(0.7)],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                    .frame(width: geometry.size.width * (Double(data.percentage) / 100), height: 8)
                                    .cornerRadius(4)
                                    .animation(.easeInOut(duration: 1.0), value: data.percentage)
                            }
                        }
                        .frame(height: 8)
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

/// Duration distribution
struct DurationDistribution: View {
    let durationRanges = [
        DurationRange(range: "< 5 min", count: 3, color: .red),
        DurationRange(range: "5-10 min", count: 12, color: .orange),
        DurationRange(range: "10-15 min", count: 45, color: .green),
        DurationRange(range: "> 15 min", count: 8, color: .blue)
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Duration Distribution")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white.opacity(0.9))
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(durationRanges, id: \.range) { data in
                    VStack(spacing: 8) {
                        Text("\(data.count)")
                            .font(.system(size: 24, weight: .bold, design: .rounded))
                            .foregroundColor(data.color)
                        
                        Text(data.range)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.center)
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(data.color.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(data.color.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

/// Time of day patterns
struct TimeOfDayPatterns: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Best Performance Times")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white.opacity(0.9))
            
            Text("Your sessions tend to be highest quality during these times:")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.6))
            
            VStack(spacing: 12) {
                TimeSlotCard(time: "8:00 - 10:00 AM", quality: 92, color: .green)
                TimeSlotCard(time: "2:00 - 4:00 PM", quality: 87, color: .blue)
                TimeSlotCard(time: "7:00 - 9:00 PM", quality: 78, color: .orange)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

/// Day of week patterns
struct DayOfWeekPatterns: View {
    let dayData = [
        DayPattern(day: "Mon", adherence: 95, color: .green),
        DayPattern(day: "Tue", adherence: 90, color: .green),
        DayPattern(day: "Wed", adherence: 85, color: .blue),
        DayPattern(day: "Thu", adherence: 88, color: .blue),
        DayPattern(day: "Fri", adherence: 82, color: .orange),
        DayPattern(day: "Sat", adherence: 65, color: .red),
        DayPattern(day: "Sun", adherence: 70, color: .orange)
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Weekly Adherence Pattern")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white.opacity(0.9))
            
            HStack(spacing: 8) {
                ForEach(dayData, id: \.day) { data in
                    VStack(spacing: 8) {
                        Text(data.day)
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.white.opacity(0.8))
                        
                        // Bar chart
                        VStack {
                            Spacer()
                            Rectangle()
                                .fill(data.color)
                                .frame(width: 30, height: CGFloat(data.adherence) * 0.8)
                                .cornerRadius(4)
                                .animation(.easeInOut(duration: 1.0), value: data.adherence)
                        }
                        .frame(height: 80)
                        
                        Text("\(data.adherence)%")
                            .font(.system(size: 10, weight: .semibold, design: .rounded))
                            .foregroundColor(data.color)
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

/// Time slot card
struct TimeSlotCard: View {
    let time: String
    let quality: Int
    let color: Color
    
    var body: some View {
        HStack {
            Text(time)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white.opacity(0.9))
            
            Spacer()
            
            Text("\(quality)%")
                .font(.system(size: 16, weight: .bold, design: .rounded))
                .foregroundColor(color)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Data Structures

struct QualityData {
    let quality: String
    let count: Int
    let color: Color
}

struct WeeklyAdherence {
    let week: String
    let completed: Int
    let planned: Int
    let color: Color
    
    var adherenceRate: Double {
        guard planned > 0 else { return 0 }
        return Double(completed) / Double(planned)
    }
}

struct AnalyticsPressureZone {
    let zone: String
    let percentage: Int
    let color: Color
    let description: String
}

struct DurationRange {
    let range: String
    let count: Int
    let color: Color
}

struct DayPattern {
    let day: String
    let adherence: Int
    let color: Color
}
