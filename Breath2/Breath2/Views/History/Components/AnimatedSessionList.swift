//
//  AnimatedSessionList.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// Magic UI-inspired animated list for displaying session history
struct AnimatedSessionList: View {
    let sessions: [Session]
    let onSessionTap: (Session) -> Void
    
    @State private var visibleItems: Set<UUID> = []
    @State private var animationDelay: Double = 0
    
    var body: some View {
        LazyVStack(spacing: 12) {
            ForEach(Array(sessions.enumerated()), id: \.element.id) { index, session in
                SessionListItem(
                    session: session,
                    onTap: { onSessionTap(session) }
                )
                .opacity(visibleItems.contains(session.id) ? 1 : 0)
                .offset(y: visibleItems.contains(session.id) ? 0 : 20)
                .animation(
                    .easeOut(duration: 0.6)
                    .delay(Double(index) * 0.1),
                    value: visibleItems.contains(session.id)
                )
                .onAppear {
                    DispatchQueue.main.asyncAfter(deadline: .now() + Double(index) * 0.05) {
                        visibleItems.insert(session.id)
                    }
                }
            }
        }
        .padding(.horizontal)
    }
}

/// Individual session list item with Magic UI styling
struct SessionListItem: View {
    let session: Session
    let onTap: () -> Void
    
    @State private var isPressed = false
    @State private var showDetails = false
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // Quality indicator with animated ring
                ZStack {
                    Circle()
                        .fill(qualityColor.opacity(0.15))
                        .frame(width: 50, height: 50)
                    
                    Circle()
                        .trim(from: 0, to: qualityProgress)
                        .stroke(
                            qualityColor,
                            style: StrokeStyle(lineWidth: 3, lineCap: .round)
                        )
                        .frame(width: 50, height: 50)
                        .rotationEffect(.degrees(-90))
                    
                    Text(qualityIcon)
                        .font(.system(size: 20))
                        .foregroundColor(qualityColor)
                }
                
                // Session details
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(sessionTitle)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white.opacity(0.9))
                        
                        Spacer()
                        
                        Text(timeAgo)
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white.opacity(0.6))
                    }
                    
                    HStack(spacing: 16) {
                        // Duration
                        HStack(spacing: 4) {
                            Image(systemName: "clock")
                                .font(.system(size: 12))
                                .foregroundColor(.white.opacity(0.6))
                            
                            Text(durationText)
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white.opacity(0.7))
                        }
                        
                        // Breath count
                        HStack(spacing: 4) {
                            Image(systemName: "wind")
                                .font(.system(size: 12))
                                .foregroundColor(.white.opacity(0.6))
                            
                            Text("\(session.breathCount) breaths")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white.opacity(0.7))
                        }
                        
                        Spacer()
                        
                        // Completion badge
                        if session.isCompleted {
                            HStack(spacing: 4) {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.system(size: 12))
                                    .foregroundColor(.green)
                                
                                Text("Complete")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(.green)
                            }
                        }
                    }
                    
                    // Progress bar
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            Rectangle()
                                .fill(Color.white.opacity(0.1))
                                .frame(height: 3)
                                .cornerRadius(1.5)
                            
                            Rectangle()
                                .fill(
                                    LinearGradient(
                                        colors: [qualityColor, qualityColor.opacity(0.7)],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(width: geometry.size.width * completionProgress, height: 3)
                                .cornerRadius(1.5)
                                .animation(.easeInOut(duration: 1.0), value: completionProgress)
                        }
                    }
                    .frame(height: 3)
                }
                
                // Chevron indicator
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.4))
                    .rotationEffect(.degrees(showDetails ? 90 : 0))
                    .animation(.easeInOut(duration: 0.3), value: showDetails)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(isPressed ? 0.1 : 0.05),
                                Color.white.opacity(isPressed ? 0.05 : 0.02)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        qualityColor.opacity(0.3),
                                        Color.white.opacity(0.1)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(
                        color: qualityColor.opacity(0.1),
                        radius: isPressed ? 15 : 8,
                        x: 0,
                        y: isPressed ? 8 : 4
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(
            minimumDuration: 0,
            maximumDistance: .infinity,
            pressing: { pressing in
                isPressed = pressing
            },
            perform: {}
        )
    }
    
    // MARK: - Computed Properties
    
    private var qualityColor: Color {
        switch session.quality {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .orange
        case .poor: return .red
        case .none: return .gray
        }
    }
    
    private var qualityIcon: String {
        switch session.quality {
        case .excellent: return "★"
        case .good: return "●"
        case .fair: return "◐"
        case .poor: return "○"
        case .none: return "?"
        }
    }
    
    private var qualityProgress: Double {
        switch session.quality {
        case .excellent: return 1.0
        case .good: return 0.75
        case .fair: return 0.5
        case .poor: return 0.25
        case .none: return 0.0
        }
    }
    
    private var sessionTitle: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM d, h:mm a"
        return formatter.string(from: session.startTime)
    }
    
    private var timeAgo: String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(session.startTime)
        
        if timeInterval < 3600 { // Less than 1 hour
            let minutes = Int(timeInterval / 60)
            return "\(minutes)m ago"
        } else if timeInterval < 86400 { // Less than 1 day
            let hours = Int(timeInterval / 3600)
            return "\(hours)h ago"
        } else {
            let days = Int(timeInterval / 86400)
            return "\(days)d ago"
        }
    }
    
    private var durationText: String {
        let minutes = Int(session.duration / 60)
        let seconds = Int(session.duration.truncatingRemainder(dividingBy: 60))
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    private var completionProgress: Double {
        guard session.plannedDuration > 0 else { return 0 }
        return min(session.duration / session.plannedDuration, 1.0)
    }
}

/// Empty state view for when no sessions are available
struct EmptySessionsView: View {
    var body: some View {
        VStack(spacing: 20) {
            // Animated breathing icon
            ZStack {
                Circle()
                    .fill(Color.cyan.opacity(0.1))
                    .frame(width: 80, height: 80)
                    .scaleEffect(1.0)
                    .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: true)
                
                Image(systemName: "wind")
                    .font(.system(size: 32, weight: .light))
                    .foregroundColor(.cyan)
            }
            
            VStack(spacing: 8) {
                Text("No Sessions Yet")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))
                
                Text("Start your first therapy session to see your progress here")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
        }
        .padding(40)
    }
}

#Preview {
    ScrollView {
        AnimatedSessionList(
            sessions: [],
            onSessionTap: { _ in }
        )
    }
    .background(
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    )
}
