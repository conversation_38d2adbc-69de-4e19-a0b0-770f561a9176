//
//  MagicMetricCard.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// Magic UI-inspired metric card with hover effects and animated content
struct MagicMetricCard: View {
    let title: String
    let value: String
    let subtitle: String?
    let icon: String
    let color: Color
    let trend: TrendDirection?
    let trendValue: String?
    
    @State private var isHovered = false
    @State private var animateValue = false
    
    enum TrendDirection {
        case up, down, neutral
        
        var icon: String {
            switch self {
            case .up: return "arrow.up.right"
            case .down: return "arrow.down.right"
            case .neutral: return "minus"
            }
        }
        
        var color: Color {
            switch self {
            case .up: return .green
            case .down: return .red
            case .neutral: return .gray
            }
        }
    }
    
    init(
        title: String,
        value: String,
        subtitle: String? = nil,
        icon: String,
        color: Color,
        trend: TrendDirection? = nil,
        trendValue: String? = nil
    ) {
        self.title = title
        self.value = value
        self.subtitle = subtitle
        self.icon = icon
        self.color = color
        self.trend = trend
        self.trendValue = trendValue
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with icon and trend
            HStack {
                // Icon with animated background
                ZStack {
                    Circle()
                        .fill(color.opacity(0.15))
                        .frame(width: 40, height: 40)
                        .scaleEffect(isHovered ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 0.3), value: isHovered)
                    
                    Image(systemName: icon)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(color)
                        .scaleEffect(animateValue ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 0.6).repeatForever(autoreverses: true), value: animateValue)
                }
                
                Spacer()
                
                // Trend indicator
                if let trend = trend, let trendValue = trendValue {
                    HStack(spacing: 4) {
                        Image(systemName: trend.icon)
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(trend.color)
                        
                        Text(trendValue)
                            .font(.system(size: 12, weight: .semibold, design: .rounded))
                            .foregroundColor(trend.color)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(trend.color.opacity(0.1))
                            .overlay(
                                Capsule()
                                    .stroke(trend.color.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
            }
            
            // Main content
            VStack(alignment: .leading, spacing: 8) {
                // Value with number ticker effect
                Text(value)
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [color, color.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .contentTransition(.numericText())
                    .scaleEffect(isHovered ? 1.05 : 1.0)
                    .animation(.easeInOut(duration: 0.3), value: isHovered)
                
                // Title
                Text(title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))
                
                // Subtitle if provided
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                        .lineLimit(2)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(isHovered ? 0.1 : 0.05),
                            Color.white.opacity(isHovered ? 0.05 : 0.02)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    color.opacity(isHovered ? 0.4 : 0.2),
                                    Color.white.opacity(isHovered ? 0.2 : 0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: isHovered ? 2 : 1
                        )
                )
                .shadow(
                    color: color.opacity(isHovered ? 0.3 : 0.1),
                    radius: isHovered ? 20 : 10,
                    x: 0,
                    y: isHovered ? 10 : 5
                )
        )
        .scaleEffect(isHovered ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.3), value: isHovered)
        .onHover { hovering in
            isHovered = hovering
        }
        .onTapGesture {
            // Simulate hover effect on tap for iOS
            withAnimation(.easeInOut(duration: 0.1)) {
                isHovered = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isHovered = false
                }
            }
        }
        .onAppear {
            // Start icon animation after a delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                animateValue = true
            }
        }
    }
}

/// Specialized metric cards for common session data
struct SessionMetricCard: View {
    let completedSessions: Int
    let totalSessions: Int
    let timeframe: String
    
    var completionRate: Double {
        guard totalSessions > 0 else { return 0 }
        return Double(completedSessions) / Double(totalSessions)
    }
    
    var body: some View {
        MagicMetricCard(
            title: "Sessions Completed",
            value: "\(completedSessions)/\(totalSessions)",
            subtitle: timeframe,
            icon: "checkmark.circle.fill",
            color: completionRate >= 0.8 ? .green : completionRate >= 0.6 ? .orange : .red,
            trend: completionRate >= 0.8 ? .up : completionRate >= 0.6 ? .neutral : .down,
            trendValue: "\(Int(completionRate * 100))%"
        )
    }
}

struct AverageQualityCard: View {
    let averageQuality: Double
    let sessionsCount: Int
    
    var qualityText: String {
        if averageQuality >= 0.9 { return "Excellent" }
        else if averageQuality >= 0.7 { return "Good" }
        else if averageQuality >= 0.5 { return "Fair" }
        else { return "Needs Work" }
    }
    
    var qualityColor: Color {
        if averageQuality >= 0.8 { return .green }
        else if averageQuality >= 0.6 { return .blue }
        else if averageQuality >= 0.4 { return .orange }
        else { return .red }
    }
    
    var body: some View {
        MagicMetricCard(
            title: "Average Quality",
            value: qualityText,
            subtitle: "Based on \(sessionsCount) sessions",
            icon: "star.fill",
            color: qualityColor,
            trend: averageQuality >= 0.7 ? .up : averageQuality >= 0.5 ? .neutral : .down,
            trendValue: "\(Int(averageQuality * 100))%"
        )
    }
}

struct StreakCard: View {
    let currentStreak: Int
    let longestStreak: Int
    
    var body: some View {
        MagicMetricCard(
            title: "Current Streak",
            value: "\(currentStreak)",
            subtitle: "Longest: \(longestStreak) days",
            icon: "flame.fill",
            color: currentStreak > 7 ? .orange : currentStreak > 3 ? .yellow : .gray,
            trend: currentStreak > 0 ? .up : .neutral,
            trendValue: "\(currentStreak) days"
        )
    }
}

#Preview {
    ScrollView {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            SessionMetricCard(
                completedSessions: 12,
                totalSessions: 14,
                timeframe: "This Week"
            )
            
            AverageQualityCard(
                averageQuality: 0.85,
                sessionsCount: 24
            )
            
            StreakCard(
                currentStreak: 8,
                longestStreak: 15
            )
            
            MagicMetricCard(
                title: "Total Time",
                value: "2.5h",
                subtitle: "This month",
                icon: "clock.fill",
                color: .cyan,
                trend: .up,
                trendValue: "+15%"
            )
        }
        .padding()
    }
    .background(
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    )
}
