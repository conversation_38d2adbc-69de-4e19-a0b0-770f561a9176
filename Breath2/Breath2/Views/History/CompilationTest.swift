//
//  CompilationTest.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// Simple compilation test to verify all components work together
struct CompilationTest: View {
    var body: some View {
        VStack {
            Text("Compilation Test")
                .font(.title)
            
            // Test basic components
            testBasicComponents
            
            // Test data manager
            testDataManager
        }
        .padding()
    }
    
    private var testBasicComponents: some View {
        VStack(spacing: 16) {
            // Test AnimatedProgressRing
            AnimatedProgressRing(
                progress: 0.75,
                value: "75%",
                subtitle: "Test"
            )
            .frame(width: 100, height: 100)
            
            // Test MagicMetricCard
            MagicMetricCard(
                title: "Test",
                value: "42",
                subtitle: "Test subtitle",
                icon: "star.fill",
                color: .blue
            )
            .frame(height: 100)
        }
    }
    
    private var testDataManager: some View {
        VStack {
            Text("Data Manager Test")
                .font(.headline)
            
            // Test that we can create a HistoryDataManager
            let historyManager = SessionHistoryManager()
            let dataManager = HistoryDataManager(sessionHistoryManager: historyManager)
            
            Text("✅ HistoryDataManager created successfully")
                .foregroundColor(.green)
        }
    }
}

/// Test that ModernHistoryView can be instantiated
struct ModernHistoryViewTest: View {
    var body: some View {
        let historyManager = SessionHistoryManager()
        let dataManager = HistoryDataManager(sessionHistoryManager: historyManager)
        
        ModernHistoryView(historyDataManager: dataManager)
    }
}

/// Test individual dashboard components
struct DashboardComponentsTest: View {
    var body: some View {
        let historyManager = SessionHistoryManager()
        let dataManager = HistoryDataManager(sessionHistoryManager: historyManager)
        
        VStack {
            Text("Dashboard Components Test")
                .font(.title2)
            
            HistoryDashboard(historyDataManager: dataManager)
        }
    }
}

/// Test timeline components
struct TimelineComponentsTest: View {
    var body: some View {
        let historyManager = SessionHistoryManager()
        let dataManager = HistoryDataManager(sessionHistoryManager: historyManager)
        
        VStack {
            Text("Timeline Components Test")
                .font(.title2)
            
            SessionTimeline(historyDataManager: dataManager)
        }
    }
}

/// Test analytics components
struct AnalyticsComponentsTest: View {
    var body: some View {
        let historyManager = SessionHistoryManager()
        let dataManager = HistoryDataManager(sessionHistoryManager: historyManager)
        
        VStack {
            Text("Analytics Components Test")
                .font(.title2)
            
            AnalyticsView(historyDataManager: dataManager)
        }
    }
}

#Preview("Basic Test") {
    CompilationTest()
}

#Preview("Modern History") {
    ModernHistoryViewTest()
}

#Preview("Dashboard") {
    DashboardComponentsTest()
}

#Preview("Timeline") {
    TimelineComponentsTest()
}

#Preview("Analytics") {
    AnalyticsComponentsTest()
}
