//
//  SessionTimeline.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// View model for session timeline
@MainActor
class SessionTimelineViewModel: ObservableObject {
    @Published var allSessions: [Session] = []
    @Published var filteredSessions: [Session] = []
    @Published var groupedSessions: [Date: [Session]] = [:]

    private let historyDataManager: HistoryDataManager

    init(historyDataManager: HistoryDataManager) {
        self.historyDataManager = historyDataManager
    }

    func loadSessions() {
        allSessions = historyDataManager.fetchRecentSessions(limit: 200)
        filteredSessions = allSessions
        groupSessions()
    }

    func refreshSessions() async {
        // Refresh sessions from the data manager
        loadSessions()
    }

    func applyFilter(_ filter: SessionTimeline.SessionFilter) {
        switch filter {
        case .all:
            filteredSessions = allSessions
        case .excellent:
            filteredSessions = allSessions.filter { $0.quality == .excellent }
        case .good:
            filteredSessions = allSessions.filter { $0.quality == .good }
        case .fair:
            filteredSessions = allSessions.filter { $0.quality == .fair }
        case .poor:
            filteredSessions = allSessions.filter { $0.quality == .poor }
        case .incomplete:
            filteredSessions = allSessions.filter { !$0.isCompleted }
        }
        groupSessions()
    }

    private func groupSessions() {
        let calendar = Calendar.current
        groupedSessions = Dictionary(grouping: filteredSessions) { session in
            calendar.startOfDay(for: session.startTime)
        }
    }
}

/// Interactive timeline view for browsing session history with Magic UI animations
struct SessionTimeline: View {
    @StateObject private var viewModel: SessionTimelineViewModel
    @State private var selectedFilter: SessionFilter = .all
    @State private var selectedSession: Session?
    @State private var showingSessionDetail = false
    @State private var searchText = ""

    init(historyDataManager: HistoryDataManager) {
        self._viewModel = StateObject(wrappedValue: SessionTimelineViewModel(historyDataManager: historyDataManager))
    }
    
    enum SessionFilter: String, CaseIterable {
        case all = "All"
        case excellent = "Excellent"
        case good = "Good"
        case fair = "Fair"
        case poor = "Poor"
        case incomplete = "Incomplete"
        
        var color: Color {
            switch self {
            case .all: return .white
            case .excellent: return .green
            case .good: return .blue
            case .fair: return .orange
            case .poor: return .red
            case .incomplete: return .gray
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with search and filters
                headerSection
                
                // Timeline content
                timelineContent
            }
            .background(backgroundGradient)
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingSessionDetail) {
            if let session = selectedSession {
                SessionDetailView(session: session)
            }
        }
        .onAppear {
            viewModel.loadSessions()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Title and back button
            HStack {
                Button(action: {
                    // Navigate back
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Text("Session History")
                    .font(.system(size: 24, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                
                Spacer()
                
                // Stats summary
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(viewModel.filteredSessions.count)")
                        .font(.system(size: 16, weight: .bold, design: .rounded))
                        .foregroundColor(.cyan)
                    
                    Text("sessions")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                }
            }
            
            // Search bar with Magic UI styling
            HStack(spacing: 12) {
                HStack(spacing: 8) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                    
                    TextField("Search sessions...", text: $searchText)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .textFieldStyle(PlainTextFieldStyle())
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
                        )
                )
                
                // Filter button
                Button(action: {
                    // Show filter options
                }) {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.cyan)
                }
            }
            
            // Filter chips
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(SessionFilter.allCases, id: \.self) { filter in
                        FilterChip(
                            title: filter.rawValue,
                            isSelected: selectedFilter == filter,
                            color: filter.color
                        ) {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                selectedFilter = filter
                                viewModel.applyFilter(filter)
                            }
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.horizontal)
        .padding(.top, 20)
        .padding(.bottom, 16)
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.05, blue: 0.08),
                    Color(red: 0.08, green: 0.08, blue: 0.12).opacity(0.8)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    // MARK: - Timeline Content
    
    private var timelineContent: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                if viewModel.filteredSessions.isEmpty {
                    EmptyTimelineView(filter: selectedFilter)
                        .padding(.top, 60)
                } else {
                    ForEach(viewModel.groupedSessions.keys.sorted(by: >), id: \.self) { date in
                        TimelineDaySection(
                            date: date,
                            sessions: viewModel.groupedSessions[date] ?? [],
                            onSessionTap: { session in
                                selectedSession = session
                                showingSessionDetail = true
                            }
                        )
                    }
                }
            }
            .padding(.bottom, 100) // Account for tab bar
        }
        .refreshable {
            await viewModel.refreshSessions()
        }
    }
    
    private var backgroundGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12),
                Color(red: 0.06, green: 0.06, blue: 0.10)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    }
}

/// Filter chip component with Magic UI styling
struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(isSelected ? .black : color)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(isSelected ? color : Color.white.opacity(0.1))
                        .overlay(
                            Capsule()
                                .stroke(color.opacity(0.5), lineWidth: 1)
                        )
                )
                .scaleEffect(isSelected ? 1.05 : 1.0)
                .shadow(
                    color: isSelected ? color.opacity(0.3) : .clear,
                    radius: isSelected ? 8 : 0,
                    x: 0,
                    y: isSelected ? 4 : 0
                )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.3), value: isSelected)
    }
}

/// Timeline day section with grouped sessions
struct TimelineDaySection: View {
    let date: Date
    let sessions: [Session]
    let onSessionTap: (Session) -> Void
    
    @State private var isExpanded = true
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Day header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(dayTitle)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white.opacity(0.9))
                    
                    Text("\(sessions.count) session\(sessions.count == 1 ? "" : "s")")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                }
                
                Spacer()
                
                // Day quality indicator
                HStack(spacing: 4) {
                    ForEach(sessions.prefix(5), id: \.id) { session in
                        Circle()
                            .fill(session.quality.color)
                            .frame(width: 8, height: 8)
                    }
                    
                    if sessions.count > 5 {
                        Text("+\(sessions.count - 5)")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white.opacity(0.6))
                    }
                }
                
                // Expand/collapse button
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                }) {
                    Image(systemName: "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                        .rotationEffect(.degrees(isExpanded ? 0 : -90))
                        .animation(.easeInOut(duration: 0.3), value: isExpanded)
                }
            }
            .padding(.horizontal)
            
            // Sessions list
            if isExpanded {
                VStack(spacing: 12) {
                    ForEach(sessions) { session in
                        TimelineSessionCard(session: session) {
                            onSessionTap(session)
                        }
                    }
                }
                .padding(.horizontal)
                .transition(.opacity.combined(with: .scale))
            }
        }
        .padding(.vertical, 16)
    }
    
    private var dayTitle: String {
        let formatter = DateFormatter()
        let calendar = Calendar.current
        
        if calendar.isDateInToday(date) {
            return "Today"
        } else if calendar.isDateInYesterday(date) {
            return "Yesterday"
        } else if calendar.isDate(date, equalTo: Date(), toGranularity: .weekOfYear) {
            formatter.dateFormat = "EEEE"
            return formatter.string(from: date)
        } else {
            formatter.dateFormat = "EEEE, MMM d"
            return formatter.string(from: date)
        }
    }
}

/// Compact session card for timeline
struct TimelineSessionCard: View {
    let session: Session
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Time indicator
                VStack(spacing: 4) {
                    Text(timeString)
                        .font(.system(size: 14, weight: .semibold, design: .rounded))
                        .foregroundColor(.white.opacity(0.9))
                    
                    Text(durationString)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                }
                .frame(width: 60)
                
                // Quality indicator line
                Rectangle()
                    .fill(session.quality.color)
                    .frame(width: 4)
                    .cornerRadius(2)
                
                // Session details
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(session.quality.description)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(session.quality.color)
                        
                        Spacer()
                        
                        if session.isCompleted {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 16))
                                .foregroundColor(.green)
                        }
                    }
                    
                    Text("\(session.breathCount) breaths • \(Int(session.averagePressure)) cmH₂O")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.4))
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(session.quality.color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var timeString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "h:mm a"
        return formatter.string(from: session.startTime)
    }
    
    private var durationString: String {
        let minutes = Int(session.duration / 60)
        let seconds = Int(session.duration.truncatingRemainder(dividingBy: 60))
        return String(format: "%d:%02d", minutes, seconds)
    }
}

/// Empty state for timeline
struct EmptyTimelineView: View {
    let filter: SessionTimeline.SessionFilter
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "clock.arrow.circlepath")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.white.opacity(0.4))
            
            VStack(spacing: 8) {
                Text(emptyTitle)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))
                
                Text(emptyMessage)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
        }
        .padding(40)
    }
    
    private var emptyTitle: String {
        switch filter {
        case .all: return "No Sessions Found"
        default: return "No \(filter.rawValue) Sessions"
        }
    }
    
    private var emptyMessage: String {
        switch filter {
        case .all: return "Start your first therapy session to see your history here"
        default: return "Try adjusting your filter or complete more sessions"
        }
    }
}

#Preview {
    let mockHistoryManager = SessionHistoryManager()
    let mockDataManager = HistoryDataManager(sessionHistoryManager: mockHistoryManager)
    return SessionTimeline(historyDataManager: mockDataManager)
}
