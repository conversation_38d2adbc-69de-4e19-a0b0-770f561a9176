//
//  HistoryDashboard.swift
//  Breath2
//
//  Created by Assistant on 18/07/2025.
//

import SwiftUI

/// View model for the history dashboard
@MainActor
class HistoryViewModel: ObservableObject {
    @Published var weeklyCompletedSessions = 0
    @Published var weeklyGoal = 7
    @Published var monthlyCompletedSessions = 0
    @Published var monthlyGoal = 30
    @Published var currentStreak = 0
    @Published var longestStreak = 0
    @Published var averageQuality = 0.0
    @Published var totalSessions = 0
    @Published var totalTimeSpent: TimeInterval = 0
    @Published var timeSpentTrend = 0.0
    @Published var recentSessions: [Session] = []
    @Published var hasRecentAchievement = false

    private let historyDataManager: HistoryDataManager

    init(historyDataManager: HistoryDataManager) {
        self.historyDataManager = historyDataManager
    }

    var totalTimeFormatted: String {
        let hours = Int(totalTimeSpent / 3600)
        let minutes = Int((totalTimeSpent.truncatingRemainder(dividingBy: 3600)) / 60)

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }

    func loadData() {
        Task {
            let dashboardData = await historyDataManager.fetchDashboardData()

            await MainActor.run {
                self.weeklyCompletedSessions = dashboardData.weeklyCompletedSessions
                self.monthlyCompletedSessions = dashboardData.monthlyCompletedSessions
                self.currentStreak = dashboardData.currentStreak
                self.longestStreak = calculateLongestStreak(from: dashboardData.sessions)
                self.averageQuality = dashboardData.averageQuality
                self.totalSessions = dashboardData.sessions.count
                self.totalTimeSpent = dashboardData.sessions.reduce(0) { $0 + $1.duration }
                self.timeSpentTrend = calculateTimeSpentTrend(from: dashboardData.sessions)
                self.hasRecentAchievement = self.currentStreak > 7
                self.recentSessions = Array(dashboardData.sessions.prefix(10))
            }
        }
    }

    func refreshData() async {
        loadData()
    }

    func qualityTrendData(for timeframe: TrendChart.TrendTimeframe) -> [TrendDataPoint] {
        let sessions = historyDataManager.fetchRecentSessions(limit: 100)
        return generateQualityTrendFromSessions(sessions, timeframe: timeframe)
    }

    func adherenceTrendData(for timeframe: TrendChart.TrendTimeframe) -> [TrendDataPoint] {
        let sessions = historyDataManager.fetchRecentSessions(limit: 100)
        return generateAdherenceTrendFromSessions(sessions, timeframe: timeframe)
    }

    // MARK: - Private Helper Methods

    private func calculateLongestStreak(from sessions: [Session]) -> Int {
        guard !sessions.isEmpty else { return 0 }

        let calendar = Calendar.current
        let sortedSessions = sessions.sorted { $0.date > $1.date }

        var longestStreak = 0
        var currentStreak = 0
        var lastDate: Date?

        for session in sortedSessions {
            let sessionDate = calendar.startOfDay(for: session.date)

            if let last = lastDate {
                let daysBetween = calendar.dateComponents([.day], from: sessionDate, to: last).day ?? 0

                if daysBetween == 1 {
                    currentStreak += 1
                } else {
                    longestStreak = max(longestStreak, currentStreak)
                    currentStreak = 1
                }
            } else {
                currentStreak = 1
            }

            lastDate = sessionDate
        }

        return max(longestStreak, currentStreak)
    }

    private func calculateTimeSpentTrend(from sessions: [Session]) -> Double {
        guard sessions.count >= 2 else { return 0 }

        let calendar = Calendar.current
        let now = Date()
        let weekAgo = calendar.date(byAdding: .weekOfYear, value: -1, to: now) ?? now
        let twoWeeksAgo = calendar.date(byAdding: .weekOfYear, value: -2, to: now) ?? now

        let thisWeek = sessions.filter { $0.date >= weekAgo }
        let lastWeek = sessions.filter { $0.date >= twoWeeksAgo && $0.date < weekAgo }

        let thisWeekTime = thisWeek.reduce(0) { $0 + $1.duration }
        let lastWeekTime = lastWeek.reduce(0) { $0 + $1.duration }

        guard lastWeekTime > 0 else { return 0 }

        return ((thisWeekTime - lastWeekTime) / lastWeekTime) * 100
    }

    private func generateQualityTrendFromSessions(_ sessions: [Session], timeframe: TrendChart.TrendTimeframe) -> [TrendDataPoint] {
        let calendar = Calendar.current
        let now = Date()

        let filteredSessions = sessions.filter { session in
            switch timeframe {
            case .week:
                return calendar.isDate(session.date, equalTo: now, toGranularity: .weekOfYear)
            case .month:
                return calendar.isDate(session.date, equalTo: now, toGranularity: .month)
            case .quarter:
                let threeMonthsAgo = calendar.date(byAdding: .month, value: -3, to: now) ?? now
                return session.date >= threeMonthsAgo
            case .year:
                return calendar.isDate(session.date, equalTo: now, toGranularity: .year)
            }
        }

        let grouped = Dictionary(grouping: filteredSessions) { session in
            calendar.startOfDay(for: session.date)
        }

        return grouped.map { date, sessions in
            let averageQuality = sessions.reduce(0.0) { sum, session in
                sum + session.quality.progressValue
            } / Double(sessions.count)

            return TrendDataPoint(date: date, value: averageQuality * 100)
        }.sorted { $0.date < $1.date }
    }

    private func generateAdherenceTrendFromSessions(_ sessions: [Session], timeframe: TrendChart.TrendTimeframe) -> [TrendDataPoint] {
        let calendar = Calendar.current

        // Group by week for adherence tracking
        let grouped = Dictionary(grouping: sessions) { session in
            calendar.dateInterval(of: .weekOfYear, for: session.date)?.start ?? session.date
        }

        return grouped.map { weekStart, sessions in
            // Calculate adherence rate (assuming 7 sessions per week as goal)
            let adherenceRate = min(Double(sessions.count) / 7.0, 1.0) * 100
            return TrendDataPoint(date: weekStart, value: adherenceRate)
        }.sorted { $0.date < $1.date }
    }
}

/// Modern, minimal history dashboard with Magic UI-inspired design
struct HistoryDashboard: View {
    @StateObject private var viewModel: HistoryViewModel
    @State private var selectedTimeframe: TrendChart.TrendTimeframe = .week
    @State private var showingSessionDetail = false
    @State private var selectedSession: Session?

    init(historyDataManager: HistoryDataManager) {
        self._viewModel = StateObject(wrappedValue: HistoryViewModel(historyDataManager: historyDataManager))
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    // Header with animated title
                    headerSection
                    
                    // Weekly progress overview
                    weeklyProgressSection
                    
                    // Key metrics grid
                    metricsGridSection
                    
                    // Trend analysis
                    trendsSection
                    
                    // Recent sessions
                    recentSessionsSection
                }
                .padding(.horizontal)
                .padding(.bottom, 100) // Account for tab bar
            }
            .background(backgroundGradient)
            .navigationBarHidden(true)
            .refreshable {
                await viewModel.refreshData()
            }
        }
        .sheet(isPresented: $showingSessionDetail) {
            if let session = selectedSession {
                SessionDetailView(session: session)
            }
        }
        .onAppear {
            viewModel.loadData()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                // Animated greeting with sparkles
                HStack(spacing: 8) {
                    Text("Your Progress")
                        .font(.system(size: 28, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.white, .white.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                    
                    // Sparkle animation for achievements
                    if viewModel.hasRecentAchievement {
                        Image(systemName: "sparkles")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.yellow, .orange],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .scaleEffect(1.0)
                            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: viewModel.hasRecentAchievement)
                    }
                }
                
                Text(motivationalMessage)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
            
            // Profile/Settings button
            Button(action: {}) {
                Image(systemName: "person.circle")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(.top, 20)
    }
    
    // MARK: - Weekly Progress Section
    
    private var weeklyProgressSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("This Week")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))
                
                Spacer()
                
                Text("Goal: \(viewModel.weeklyGoal) sessions")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
            }
            
            WeeklyAdherenceRing(
                completedSessions: viewModel.weeklyCompletedSessions,
                plannedSessions: viewModel.weeklyGoal,
                currentStreak: viewModel.currentStreak
            )
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.08),
                            Color.white.opacity(0.04)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.cyan.opacity(0.3),
                                    Color.white.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: Color.cyan.opacity(0.1),
                    radius: 15,
                    x: 0,
                    y: 8
                )
        )
    }
    
    // MARK: - Metrics Grid Section
    
    private var metricsGridSection: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            SessionMetricCard(
                completedSessions: viewModel.monthlyCompletedSessions,
                totalSessions: viewModel.monthlyGoal,
                timeframe: "This Month"
            )
            
            AverageQualityCard(
                averageQuality: viewModel.averageQuality,
                sessionsCount: viewModel.totalSessions
            )
            
            StreakCard(
                currentStreak: viewModel.currentStreak,
                longestStreak: viewModel.longestStreak
            )
            
            MagicMetricCard(
                title: "Total Time",
                value: viewModel.totalTimeFormatted,
                subtitle: "All sessions",
                icon: "clock.fill",
                color: .purple,
                trend: viewModel.timeSpentTrend > 0 ? .up : viewModel.timeSpentTrend < 0 ? .down : .neutral,
                trendValue: viewModel.timeSpentTrend != 0 ? "\(abs(Int(viewModel.timeSpentTrend)))%" : nil
            )
        }
    }
    
    // MARK: - Trends Section
    
    private var trendsSection: some View {
        VStack(spacing: 16) {
            // Timeframe selector
            HStack {
                Text("Trends")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))
                
                Spacer()
                
                Picker("Timeframe", selection: $selectedTimeframe) {
                    ForEach(TrendChart.TrendTimeframe.allCases, id: \.self) { timeframe in
                        Text(timeframe.rawValue)
                            .tag(timeframe)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 200)
            }
            
            // Quality trend chart
            TrendChart(
                dataPoints: viewModel.qualityTrendData(for: selectedTimeframe),
                title: "Session Quality",
                color: .cyan,
                timeframe: selectedTimeframe
            )
            
            // Adherence trend chart
            TrendChart(
                dataPoints: viewModel.adherenceTrendData(for: selectedTimeframe),
                title: "Weekly Adherence",
                color: .green,
                timeframe: selectedTimeframe
            )
        }
    }
    
    // MARK: - Recent Sessions Section
    
    private var recentSessionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Sessions")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white.opacity(0.9))
                
                Spacer()
                
                Button("View All") {
                    // Navigate to full session history
                }
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.cyan)
            }
            
            if viewModel.recentSessions.isEmpty {
                EmptySessionsView()
            } else {
                AnimatedSessionList(
                    sessions: Array(viewModel.recentSessions.prefix(5)),
                    onSessionTap: { session in
                        selectedSession = session
                        showingSessionDetail = true
                    }
                )
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var backgroundGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.08),
                Color(red: 0.08, green: 0.08, blue: 0.12),
                Color(red: 0.06, green: 0.06, blue: 0.10)
            ],
            startPoint: .top,
            endPoint: .bottom
        )
    }
    
    private var motivationalMessage: String {
        let adherenceRate = Double(viewModel.weeklyCompletedSessions) / Double(max(viewModel.weeklyGoal, 1))
        
        if adherenceRate >= 1.0 {
            return "Excellent work! You've exceeded your weekly goal 🎉"
        } else if adherenceRate >= 0.8 {
            return "Great progress! You're almost at your weekly goal 💪"
        } else if adherenceRate >= 0.5 {
            return "Keep going! You're halfway to your weekly goal 🌟"
        } else if viewModel.currentStreak > 0 {
            return "Nice streak! Keep the momentum going 🔥"
        } else {
            return "Ready to start your therapy journey? 🌱"
        }
    }
}

#Preview {
    let mockHistoryManager = SessionHistoryManager()
    let mockDataManager = HistoryDataManager(sessionHistoryManager: mockHistoryManager)
    return HistoryDashboard(historyDataManager: mockDataManager)
}
