//
//  BreathDetectionTuningView.swift
//  Breath2
//
//  UI for testing and tuning breath detection parameters
//

import SwiftUI

struct BreathDetectionTuningView: View {
    @ObservedObject var audioManager: AudioManager
    @State private var sensitivity: Float = 0.5
    @State private var responsiveness: Float = 0.5
    @State private var selectedPreset: String = "Standard"
    
    private let presets = ["Sensitive", "Standard", "Stable"]
    
    var body: some View {
        VStack(spacing: 20) {
            
            // Header
            VStack(spacing: 8) {
                Text("Breath Detection Tuning")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Adjust parameters to make breath duration more reliable")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // Current Status
            VStack(spacing: 12) {
                HStack {
                    Text("Current Breath:")
                        .font(.headline)
                    Spacer()
                    Text(audioManager.breathDetector.statusDescription)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Duration")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("\(audioManager.breathDetector.currentDurationText)s")
                            .font(.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(audioManager.breathDetector.isBreathing ? .green : .primary)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Total Breaths")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(audioManager.breathDetector.breathCountText)
                            .font(.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                    }
                }
                
                // Signal strength indicators
                HStack(spacing: 16) {
                    VStack(spacing: 4) {
                        Text("Audio")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        ProgressView(value: min(Double(audioManager.audioLevel) / 0.05, 1.0))
                            .progressViewStyle(LinearProgressViewStyle(tint: .orange))
                            .frame(width: 60)
                    }
                    
                    VStack(spacing: 4) {
                        Text("Pressure")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        ProgressView(value: min(audioManager.currentPressure / 25.0, 1.0))
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            .frame(width: 60)
                    }
                    
                    VStack(spacing: 4) {
                        Text("Frequency")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        ProgressView(value: min(audioManager.currentFrequency / 50.0, 1.0))
                            .progressViewStyle(LinearProgressViewStyle(tint: .green))
                            .frame(width: 60)
                    }
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            
            // Preset Selection
            VStack(alignment: .leading, spacing: 8) {
                Text("Quick Presets")
                    .font(.headline)
                
                HStack(spacing: 12) {
                    ForEach(presets, id: \.self) { preset in
                        Button(action: {
                            applyPreset(preset)
                        }) {
                            Text(preset)
                                .font(.subheadline)
                                .fontWeight(selectedPreset == preset ? .semibold : .regular)
                                .foregroundColor(selectedPreset == preset ? .white : .primary)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(selectedPreset == preset ? Color.blue : Color(.systemGray5))
                                .cornerRadius(8)
                        }
                    }
                }
            }
            
            // Manual Tuning
            VStack(alignment: .leading, spacing: 16) {
                Text("Manual Tuning")
                    .font(.headline)
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Sensitivity")
                            .font(.subheadline)
                        Spacer()
                        Text(String(format: "%.1f", sensitivity))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Stable")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Slider(value: $sensitivity, in: 0...1, step: 0.1) { _ in
                            applyCustomSettings()
                        }
                        
                        Text("Sensitive")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Text("Higher sensitivity detects breaths more easily but may be less stable")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Responsiveness")
                            .font(.subheadline)
                        Spacer()
                        Text(String(format: "%.1f", responsiveness))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Slow")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Slider(value: $responsiveness, in: 0...1, step: 0.1) { _ in
                            applyCustomSettings()
                        }
                        
                        Text("Fast")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Text("Higher responsiveness reacts faster to changes but may be more jittery")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            // Reset Button
            Button("Reset Breath Counter") {
                audioManager.breathDetector.reset()
            }
            .foregroundColor(.red)
            .padding(.top)
            
            Spacer()
        }
        .padding()
        .navigationTitle("Breath Tuning")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private func applyPreset(_ preset: String) {
        selectedPreset = preset
        
        switch preset {
        case "Sensitive":
            sensitivity = 0.8
            responsiveness = 0.7
            audioManager.updateBreathDetectionConfig(.sensitive)
            
        case "Standard":
            sensitivity = 0.5
            responsiveness = 0.5
            audioManager.updateBreathDetectionConfig(.standard)
            
        case "Stable":
            sensitivity = 0.2
            responsiveness = 0.3
            audioManager.updateBreathDetectionConfig(.stable)
            
        default:
            break
        }
    }
    
    private func applyCustomSettings() {
        selectedPreset = "Custom"
        audioManager.setBreathDetectionSensitivity(sensitivity, responsiveness: responsiveness)
    }
}

// MARK: - Preview

struct BreathDetectionTuningView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            BreathDetectionTuningView(audioManager: AudioManager())
        }
    }
}