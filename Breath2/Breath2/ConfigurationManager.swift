//
//  ConfigurationManager.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//
//  Manages algorithm configuration loading, saving, and application

import Foundation
import SwiftUI

// MARK: - Configuration Manager

class ConfigurationManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentConfiguration: AlgorithmConfiguration
    @Published var selectedPreset: ConfigurationPreset
    @Published var isCustomConfiguration: Bool = false

    // MARK: - Performance Monitoring

    /// Performance metrics for monitoring configuration impact
    struct PerformanceMetrics {
        var processingTime: TimeInterval = 0
        var memoryUsage: UInt64 = 0
        var detectionRate: Double = 0
        var averageLatency: TimeInterval = 0
        var cpuUsage: Double = 0
        var samplesProcessed: Int = 0
        var successfulDetections: Int = 0
        var bufferOverruns: Int = 0
        var droppedSamples: Int = 0
    }

    @Published var performanceMetrics = ConfigurationManager.PerformanceMetrics()

    // MARK: - Private Properties

    private let userDefaults = UserDefaults.standard
    private let configurationKey = "AlgorithmConfiguration"
    private let presetKey = "SelectedPreset"
    
    // MARK: - Initialization

    init() {
        // Initialize with STRICT paper-compliant configuration ONLY
        self.currentConfiguration = AlgorithmConfiguration.paperCompliant
        self.selectedPreset = ConfigurationPreset.paperCompliant

        // Load saved configuration
        loadConfiguration()
    }
    
    // MARK: - Configuration Management
    
    /// Load configuration from UserDefaults
    func loadConfiguration() {
        // Load selected preset
        if let presetString = userDefaults.string(forKey: presetKey),
           let preset = ConfigurationPreset(rawValue: presetString) {
            selectedPreset = preset
        }
        
        // Load custom configuration if available
        if let configData = userDefaults.data(forKey: configurationKey) {
            do {
                let decoder = JSONDecoder()
                currentConfiguration = try decoder.decode(AlgorithmConfiguration.self, from: configData)
                isCustomConfiguration = (selectedPreset == .custom)
            } catch {
                print("Failed to load configuration: \(error)")
                currentConfiguration = .paperCompliant
                selectedPreset = .paperCompliant
                isCustomConfiguration = false
            }
        } else {
            // No saved configuration, use preset
            currentConfiguration = AlgorithmConfiguration.configuration(for: selectedPreset)
            isCustomConfiguration = false
        }
    }
    
    /// Save current configuration to UserDefaults
    func saveConfiguration() {
        do {
            let encoder = JSONEncoder()
            let configData = try encoder.encode(currentConfiguration)
            userDefaults.set(configData, forKey: configurationKey)
            userDefaults.set(selectedPreset.rawValue, forKey: presetKey)
            print("Configuration saved successfully")
        } catch {
            print("Failed to save configuration: \(error)")
        }
    }
    
    /// Apply a preset configuration
    func applyPreset(_ preset: ConfigurationPreset) {
        selectedPreset = preset
        
        if preset == .custom {
            isCustomConfiguration = true
        } else {
            currentConfiguration = AlgorithmConfiguration.configuration(for: preset)
            isCustomConfiguration = false
        }
        
        saveConfiguration()
    }
    
    /// Update configuration and mark as custom
    func updateConfiguration(_ newConfiguration: AlgorithmConfiguration) {
        currentConfiguration = newConfiguration
        selectedPreset = .custom
        isCustomConfiguration = true
        saveConfiguration()
    }
    
    /// Reset to paper-compliant configuration (ONLY allowed configuration)
    func resetToPaperCompliant() {
        applyPreset(.paperCompliant)
    }
    
    /// Validate current configuration
    func validateCurrentConfiguration() -> (isValid: Bool, errors: [String]) {
        return currentConfiguration.validate()
    }
    
    // MARK: - Quick Adjustments
    
    /// Quick adjustment methods for common tweaks
    
    func adjustLatency(_ mode: LatencyMode) {
        var config = currentConfiguration
        
        switch mode {
        case .ultraLow:
            config.bufferSize = 0.05
            config.targetBufferLength = 150
            config.correlationThreshold = 0.5
        case .low:
            config.bufferSize = 0.08
            config.targetBufferLength = 200
            config.correlationThreshold = 0.55
        case .balanced:
            config.bufferSize = 0.1
            config.targetBufferLength = 300
            config.correlationThreshold = 0.6
        case .high:
            config.bufferSize = 0.15
            config.targetBufferLength = 400
            config.correlationThreshold = 0.65
        }
        
        updateConfiguration(config)
    }
    
    func adjustSmoothness(_ level: SmoothnessLevel) {
        var config = currentConfiguration
        
        switch level {
        case .minimal:
            config.decayRate = 0.6
            config.maxRunLength = 3
            config.leapThreshold = 0.25
        case .light:
            config.decayRate = 0.7
            config.maxRunLength = 4
            config.leapThreshold = 0.22
        case .balanced:
            config.decayRate = 0.8
            config.maxRunLength = 5
            config.leapThreshold = 0.20
        case .heavy:
            config.decayRate = 0.85
            config.maxRunLength = 6
            config.leapThreshold = 0.15
        case .maximum:
            config.decayRate = 0.9
            config.maxRunLength = 8
            config.leapThreshold = 0.12
        }
        
        updateConfiguration(config)
    }
    
    func adjustAccuracy(_ mode: AccuracyMode) {
        var config = currentConfiguration
        
        switch mode {
        case .fast:
            config.coarseStep = 4
            config.fineSearchWindow = 3
            config.downsampleFactorOverride = 55
        case .balanced:
            config.coarseStep = 3
            config.fineSearchWindow = 5
            config.downsampleFactorOverride = nil // Use calculated value
        case .precise:
            config.coarseStep = 2
            config.fineSearchWindow = 7
            config.downsampleFactorOverride = 35
        case .research:
            config.coarseStep = 1
            config.fineSearchWindow = 10
            config.downsampleFactorOverride = 30
        }
        
        updateConfiguration(config)
    }
    
    func adjustSensitivity(_ level: SensitivityLevel) {
        var config = currentConfiguration
        
        switch level {
        case .low:
            config.correlationThreshold = 0.7
            config.minFreq = 10
            config.maxFreq = 35
        case .medium:
            config.correlationThreshold = 0.6
            config.minFreq = 8
            config.maxFreq = 40
        case .high:
            config.correlationThreshold = 0.55
            config.minFreq = 7
            config.maxFreq = 42
        case .maximum:
            config.correlationThreshold = 0.5
            config.minFreq = 5
            config.maxFreq = 45
        }
        
        updateConfiguration(config)
    }
    
    // MARK: - Export/Import
    
    /// Export configuration as JSON string
    func exportConfiguration() -> String {
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let data = try encoder.encode(currentConfiguration)
            return String(data: data, encoding: .utf8) ?? "Export failed"
        } catch {
            print("Failed to export configuration: \(error)")
            return "Export failed: \(error.localizedDescription)"
        }
    }
    
    /// Import configuration from JSON string
    func importConfiguration(from jsonString: String) -> Bool {
        guard let data = jsonString.data(using: .utf8) else {
            return false
        }
        
        do {
            let decoder = JSONDecoder()
            let configuration = try decoder.decode(AlgorithmConfiguration.self, from: data)
            
            // Validate imported configuration
            let validation = configuration.validate()
            if validation.isValid {
                updateConfiguration(configuration)
                return true
            } else {
                print("Invalid imported configuration: \(validation.errors)")
                return false
            }
        } catch {
            print("Failed to import configuration: \(error)")
            return false
        }
    }

    // MARK: - Performance Monitoring Methods

    /// Update performance metrics
    func updatePerformanceMetrics(
        processingTime: TimeInterval,
        memoryUsage: UInt64,
        detectionRate: Double,
        latency: TimeInterval,
        cpuUsage: Double
    ) {
        DispatchQueue.main.async {
            self.performanceMetrics.processingTime = processingTime
            self.performanceMetrics.memoryUsage = memoryUsage
            self.performanceMetrics.detectionRate = detectionRate
            self.performanceMetrics.averageLatency = latency
            self.performanceMetrics.cpuUsage = cpuUsage
        }
    }

    /// Check if current configuration meets performance targets
    func meetsPerformanceTargets() -> Bool {
        let metrics = performanceMetrics

        // Performance targets based on paper specifications
        let maxProcessingTime: TimeInterval = Double(currentConfiguration.bufferSize) * 0.1 // 10% of buffer duration
        let maxMemoryUsage: UInt64 = 50_000_000 // 50MB
        let minDetectionRate: Double = 0.8 // 80%
        let maxLatency: TimeInterval = 0.3 // 300ms
        let maxCpuUsage: Double = 0.3 // 30%

        return metrics.processingTime <= maxProcessingTime &&
               metrics.memoryUsage <= maxMemoryUsage &&
               metrics.detectionRate >= minDetectionRate &&
               metrics.averageLatency <= maxLatency &&
               metrics.cpuUsage <= maxCpuUsage
    }

    /// Get performance status description
    var performanceStatus: String {
        if meetsPerformanceTargets() {
            return "✅ Performance targets met"
        } else {
            return "⚠️ Performance targets not met"
        }
    }

    /// Reset performance metrics
    func resetPerformanceMetrics() {
        performanceMetrics = PerformanceMetrics()
        print("🔄 Performance metrics reset")
    }
}

// MARK: - Quick Adjustment Enums

enum LatencyMode: String, CaseIterable {
    case ultraLow = "Ultra Low (~50ms)"
    case low = "Low (~80ms)"
    case balanced = "Balanced (~100ms)"
    case high = "High (~150ms)"
}

enum SmoothnessLevel: String, CaseIterable {
    case minimal = "Minimal"
    case light = "Light"
    case balanced = "Balanced"
    case heavy = "Heavy"
    case maximum = "Maximum"
}

enum AccuracyMode: String, CaseIterable {
    case fast = "Fast"
    case balanced = "Balanced"
    case precise = "Precise"
    case research = "Research"
}

enum SensitivityLevel: String, CaseIterable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case maximum = "Maximum"
}
