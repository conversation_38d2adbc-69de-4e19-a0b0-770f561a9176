//
//  AudioParametersView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Development UI for adjusting audio-specific parameters

import SwiftUI

struct AudioParametersView: View {
    @ObservedObject var configManager: ConfigurationManager
    @State private var config: AlgorithmConfiguration
    
    init(configManager: ConfigurationManager) {
        self.configManager = configManager
        self._config = State(initialValue: configManager.currentConfiguration)
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                headerSection
                audioInputSection
                processingSection
                filteringSection
                debugSection
            }
            .padding()
        }
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.05, blue: 0.08),
                    Color(red: 0.08, green: 0.08, blue: 0.12),
                    Color(red: 0.10, green: 0.10, blue: 0.15)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .onChange(of: config) { _, newConfig in
            configManager.updateConfiguration(newConfig)
        }
        .onReceive(configManager.$currentConfiguration) { newConfig in
            config = newConfig
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "mic.fill")
                    .font(.title2)
                    .foregroundColor(.cyan)
                
                Text("Audio Parameters")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            Text("Configure audio input, processing, and filtering settings")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
    }
    
    // MARK: - Audio Input Section
    
    private var audioInputSection: some View {
        ParameterSection(title: "Audio Input", icon: "waveform.circle") {
            VStack(spacing: 16) {
                ParameterSlider(
                    title: "Sample Rate",
                    value: Binding(
                        get: { Double(config.sampleRate) },
                        set: { config.sampleRate = Float($0) }
                    ),
                    range: 22050...48000,
                    unit: "Hz",
                    description: "Audio sampling frequency (higher = better quality, more CPU)"
                )
                
                ParameterSlider(
                    title: "Buffer Size",
                    value: Binding(
                        get: { Double(config.bufferSize) },
                        set: { config.bufferSize = Float($0) }
                    ),
                    range: 0.05...0.5,
                    unit: "sec",
                    description: "Audio buffer duration (lower = less latency, more CPU)"
                )
                
                ParameterSlider(
                    title: "Lower Formant Frequency",
                    value: Binding(
                        get: { Double(config.lowerFormantFreq) },
                        set: { config.lowerFormantFreq = Int($0) }
                    ),
                    range: 100...500,
                    unit: "Hz",
                    description: "High-pass filter cutoff for voice formants"
                )
            }
        }
    }
    
    // MARK: - Processing Section
    
    private var processingSection: some View {
        ParameterSection(title: "Signal Processing", icon: "waveform.path") {
            VStack(spacing: 16) {
                if let downsampleFactor = config.downsampleFactorOverride {
                    StepperParameter(
                        title: "Downsample Factor",
                        value: Binding(
                            get: { downsampleFactor },
                            set: { config.downsampleFactorOverride = $0 }
                        ),
                        range: 10...100,
                        description: "Reduce sample rate by this factor (higher = less CPU)"
                    )
                } else {
                    HStack {
                        Text("Downsample Factor")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Button("Enable") {
                            config.downsampleFactorOverride = 40
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.cyan)
                    }
                }
                
                StepperParameter(
                    title: "Min Data Check",
                    value: Binding(
                        get: { config.minDataCheck },
                        set: { config.minDataCheck = $0 }
                    ),
                    range: 50...500,
                    description: "Minimum samples required for processing"
                )
                
                if let smoothingSize = config.smoothingFilterSizeOverride {
                    StepperParameter(
                        title: "Smoothing Filter Size",
                        value: Binding(
                            get: { smoothingSize },
                            set: { config.smoothingFilterSizeOverride = $0 }
                        ),
                        range: 50...500,
                        description: "Size of smoothing filter (higher = more smoothing)"
                    )
                } else {
                    HStack {
                        Text("Smoothing Filter Size")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Button("Enable") {
                            config.smoothingFilterSizeOverride = 200
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.cyan)
                    }
                }
            }
        }
    }
    
    // MARK: - Filtering Section
    
    private var filteringSection: some View {
        ParameterSection(title: "Pressure Calculation", icon: "gauge") {
            VStack(spacing: 16) {
                ParameterSlider(
                    title: "Pressure Slope",
                    value: Binding(
                        get: { Double(config.pressureSlope) },
                        set: { config.pressureSlope = Float($0) }
                    ),
                    range: 0.5...2.0,
                    unit: "",
                    description: "Linear conversion slope from frequency to pressure"
                )
                
                ParameterSlider(
                    title: "Pressure Intercept",
                    value: Binding(
                        get: { Double(config.pressureIntercept) },
                        set: { config.pressureIntercept = Float($0) }
                    ),
                    range: -10...0,
                    unit: "",
                    description: "Linear conversion intercept from frequency to pressure"
                )
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Pressure Formula")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                    
                    Text("Pressure = \(String(format: "%.3f", config.pressureSlope)) × Frequency + \(String(format: "%.3f", config.pressureIntercept))")
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.cyan)
                        .padding(8)
                        .background(Color.white.opacity(0.05))
                        .cornerRadius(6)
                }
            }
        }
    }
    
    // MARK: - Debug Section
    
    private var debugSection: some View {
        ParameterSection(title: "Debug & Logging", icon: "ladybug") {
            VStack(spacing: 16) {
                ToggleParameter(
                    title: "Save Results",
                    isOn: Binding(
                        get: { config.saveResults },
                        set: { config.saveResults = $0 }
                    ),
                    description: "Save processing results to files for analysis"
                )
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Current Configuration")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        Text(configSummary)
                            .font(.system(.caption, design: .monospaced))
                            .foregroundColor(.secondary)
                            .padding(8)
                            .background(Color.white.opacity(0.05))
                            .cornerRadius(6)
                    }
                }
                
                HStack {
                    Button("Reset Audio Settings") {
                        resetAudioSettings()
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.orange)
                    
                    Spacer()
                    
                    Button("Apply Optimized") {
                        applyOptimizedSettings()
                    }
                    .buttonStyle(.borderedProminent)
                    .foregroundColor(.white)
                }
            }
        }
    }
    
    // MARK: - Helper Properties
    
    private var configSummary: String {
        """
        Sample Rate: \(Int(config.sampleRate))Hz
        Buffer: \(String(format: "%.3f", config.bufferSize))s
        Downsample: \(config.downsampleFactorOverride?.description ?? "Auto")
        Formant: \(Int(config.lowerFormantFreq))Hz
        """
    }
    
    // MARK: - Helper Methods
    
    private func resetAudioSettings() {
        let defaultConfig = AlgorithmConfiguration.standard
        config.sampleRate = defaultConfig.sampleRate
        config.bufferSize = defaultConfig.bufferSize
        config.lowerFormantFreq = defaultConfig.lowerFormantFreq
        config.downsampleFactorOverride = defaultConfig.downsampleFactorOverride
        config.minDataCheck = defaultConfig.minDataCheck
        config.smoothingFilterSizeOverride = defaultConfig.smoothingFilterSizeOverride
        config.pressureSlope = defaultConfig.pressureSlope
        config.pressureIntercept = defaultConfig.pressureIntercept
    }
    
    private func applyOptimizedSettings() {
        // Apply settings optimized for real-time performance
        config.sampleRate = 44100
        config.bufferSize = 0.1
        config.downsampleFactorOverride = 40
        config.minDataCheck = 100
        config.smoothingFilterSizeOverride = 200
    }
}

#Preview {
    AudioParametersView(configManager: ConfigurationManager())
}
