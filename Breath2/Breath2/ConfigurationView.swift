//
//  ConfigurationView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  SwiftUI interface for algorithm configuration and testing

import SwiftUI

struct ConfigurationView: View {
    var body: some View {
        NavigationView {
            #if DEBUG
            DeveloperSettingsView()
            #else
            ProductionConfigurationView()
            #endif
        }
    }
}

// MARK: - Production View (Minimal/Disabled)
struct ProductionConfigurationView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "gearshape.fill")
                .font(.system(size: 60))
                .foregroundColor(.cyan)
                .padding()

            Text("Algorithm Configuration")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            Text("Configuration interface disabled in production")
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding()

            Text("The breath detection system is active!\nYou can see breath counts during therapy sessions.")
                .font(.subheadline)
                .foregroundColor(.cyan)
                .multilineTextAlignment(.center)
                .padding()

            Spacer()
        }
        .padding()
        .navigationTitle("Settings")
        .navigationBarTitleDisplayMode(.large)
        .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
    }
}

#Preview {
    ConfigurationView()
}
