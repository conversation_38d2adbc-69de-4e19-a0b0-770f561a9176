//
//  ParameterComponents.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Reusable UI components for parameter adjustment

import SwiftUI

// MARK: - Parameter Section Container

struct ParameterSection<Content: View>: View {
    let title: String
    let icon: String
    let content: Content
    
    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.cyan)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            content
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

// MARK: - Parameter Slider

struct ParameterSlider: View {
    let title: String
    @Binding var value: Double
    let range: ClosedRange<Double>
    let unit: String
    let description: String
    let isLogarithmic: Bool
    
    init(
        title: String,
        value: Binding<Double>,
        range: ClosedRange<Double>,
        unit: String,
        description: String,
        isLogarithmic: Bool = false
    ) {
        self.title = title
        self._value = value
        self.range = range
        self.unit = unit
        self.description = description
        self.isLogarithmic = isLogarithmic
    }
    
    private var displayValue: Double {
        if isLogarithmic {
            return log10(value)
        }
        return value
    }
    
    private var displayRange: ClosedRange<Double> {
        if isLogarithmic {
            return log10(range.lowerBound)...log10(range.upperBound)
        }
        return range
    }
    
    private func setDisplayValue(_ newValue: Double) {
        if isLogarithmic {
            value = pow(10, newValue)
        } else {
            value = newValue
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text(formatValue(value) + (unit.isEmpty ? "" : " \(unit)"))
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.cyan)
                    .monospacedDigit()
            }
            
            Slider(
                value: Binding(
                    get: { displayValue },
                    set: { setDisplayValue($0) }
                ),
                in: displayRange
            ) {
                Text(title)
            }
            .accentColor(.cyan)
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func formatValue(_ value: Double) -> String {
        if isLogarithmic {
            return String(format: "%.2e", value)
        } else if value < 1 {
            return String(format: "%.3f", value)
        } else if value < 10 {
            return String(format: "%.2f", value)
        } else {
            return String(format: "%.1f", value)
        }
    }
}

// MARK: - Stepper Parameter

struct StepperParameter: View {
    let title: String
    @Binding var value: Int
    let range: ClosedRange<Int>
    let description: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Spacer()
                
                HStack(spacing: 12) {
                    Button {
                        if value > range.lowerBound {
                            value -= 1
                        }
                    } label: {
                        Image(systemName: "minus.circle.fill")
                            .font(.title3)
                            .foregroundColor(value > range.lowerBound ? .cyan : .gray)
                    }
                    .disabled(value <= range.lowerBound)
                    
                    Text("\(value)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.cyan)
                        .monospacedDigit()
                        .frame(minWidth: 30)
                    
                    Button {
                        if value < range.upperBound {
                            value += 1
                        }
                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(.title3)
                            .foregroundColor(value < range.upperBound ? .cyan : .gray)
                    }
                    .disabled(value >= range.upperBound)
                }
            }
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Toggle Parameter

struct ToggleParameter: View {
    let title: String
    @Binding var isOn: Bool
    let description: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Spacer()
                
                Toggle("", isOn: $isOn)
                    .toggleStyle(SwitchToggleStyle(tint: .cyan))
            }
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Picker Parameter

struct PickerParameter<T: Hashable & CaseIterable & RawRepresentable>: View where T.RawValue == String {
    let title: String
    @Binding var selection: T
    let description: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            Picker(title, selection: $selection) {
                ForEach(Array(T.allCases), id: \.self) { option in
                    Text(option.rawValue)
                        .tag(option)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .colorScheme(.dark)
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Status Indicator

struct StatusIndicator: View {
    let title: String
    let value: String
    let status: StatusType
    let icon: String
    
    enum StatusType {
        case good, warning, error, neutral
        
        var color: Color {
            switch self {
            case .good: return .green
            case .warning: return .orange
            case .error: return .red
            case .neutral: return .cyan
            }
        }
    }
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(status.color)
            
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(status.color)
                .monospacedDigit()
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Performance Meter

struct PerformanceMeter: View {
    let title: String
    let value: Double
    let range: ClosedRange<Double>
    let unit: String
    let warningThreshold: Double
    let errorThreshold: Double
    
    private var normalizedValue: Double {
        (value - range.lowerBound) / (range.upperBound - range.lowerBound)
    }
    
    private var status: StatusIndicator.StatusType {
        if value >= errorThreshold {
            return .error
        } else if value >= warningThreshold {
            return .warning
        } else {
            return .good
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text(String(format: "%.2f", value) + " \(unit)")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(status.color)
                    .monospacedDigit()
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.white.opacity(0.1))
                        .frame(height: 6)
                        .cornerRadius(3)
                    
                    Rectangle()
                        .fill(status.color)
                        .frame(width: geometry.size.width * normalizedValue, height: 6)
                        .cornerRadius(3)
                        .animation(.easeInOut(duration: 0.3), value: normalizedValue)
                }
            }
            .frame(height: 6)
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        ParameterSection(title: "Test Section", icon: "gear") {
            VStack(spacing: 16) {
                ParameterSlider(
                    title: "Test Slider",
                    value: .constant(0.5),
                    range: 0...1,
                    unit: "Hz",
                    description: "This is a test slider"
                )
                
                StepperParameter(
                    title: "Test Stepper",
                    value: .constant(5),
                    range: 1...10,
                    description: "This is a test stepper"
                )
                
                ToggleParameter(
                    title: "Test Toggle",
                    isOn: .constant(true),
                    description: "This is a test toggle"
                )
            }
        }
    }
    .padding()
    .background(Color.black)
}
