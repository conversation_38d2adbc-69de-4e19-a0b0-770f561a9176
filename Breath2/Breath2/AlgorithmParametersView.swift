//
//  AlgorithmParametersView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Development UI for adjusting algorithm-specific parameters

import SwiftUI

struct AlgorithmParametersView: View {
    @ObservedObject var configManager: ConfigurationManager
    @State private var config: AlgorithmConfiguration
    
    init(configManager: ConfigurationManager) {
        self.configManager = configManager
        self._config = State(initialValue: configManager.currentConfiguration)
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                headerSection
                frequencySection
                correlationSection
                smoothingSection
                performanceSection
                validationSection
            }
            .padding()
        }
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.05, green: 0.05, blue: 0.08),
                    Color(red: 0.08, green: 0.08, blue: 0.12),
                    Color(red: 0.10, green: 0.10, blue: 0.15)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .onChange(of: config) { _, newConfig in
            configManager.updateConfiguration(newConfig)
        }
        .onReceive(configManager.$currentConfiguration) { newConfig in
            config = newConfig
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "waveform.path.ecg")
                    .font(.title2)
                    .foregroundColor(.cyan)
                
                Text("Algorithm Parameters")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            Text("Adjust core algorithm settings for pitch detection and signal processing")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
    }
    
    // MARK: - Frequency Section
    
    private var frequencySection: some View {
        ParameterSection(title: "Frequency Range", icon: "waveform") {
            VStack(spacing: 16) {
                ParameterSlider(
                    title: "Min Frequency",
                    value: Binding(
                        get: { Double(config.minFreq) },
                        set: { config.minFreq = Int($0) }
                    ),
                    range: 5...15,
                    unit: "Hz",
                    description: "Lower bound for frequency detection"
                )
                
                ParameterSlider(
                    title: "Max Frequency",
                    value: Binding(
                        get: { Double(config.maxFreq) },
                        set: { config.maxFreq = Int($0) }
                    ),
                    range: 25...60,
                    unit: "Hz",
                    description: "Upper bound for frequency detection"
                )
                
                ParameterSlider(
                    title: "Frequency Accuracy",
                    value: Binding(
                        get: { Double(config.freqAccuracy) },
                        set: { config.freqAccuracy = Float($0) }
                    ),
                    range: 0.01...0.1,
                    unit: "Hz",
                    description: "Precision of frequency measurements"
                )
            }
        }
    }
    
    // MARK: - Correlation Section
    
    private var correlationSection: some View {
        ParameterSection(title: "Correlation & Detection", icon: "target") {
            VStack(spacing: 16) {
                ParameterSlider(
                    title: "Correlation Threshold",
                    value: Binding(
                        get: { Double(config.correlationThreshold) },
                        set: { config.correlationThreshold = Float($0) }
                    ),
                    range: 0.3...0.9,
                    unit: "",
                    description: "Minimum correlation for valid detection"
                )
                
                ParameterSlider(
                    title: "Min Amplitude",
                    value: Binding(
                        get: { Double(config.minAmp) },
                        set: { config.minAmp = Float($0) }
                    ),
                    range: 1.0e-5...1.0e-3,
                    unit: "",
                    description: "Minimum signal amplitude threshold",
                    isLogarithmic: true
                )
                
                StepperParameter(
                    title: "Coarse Step",
                    value: Binding(
                        get: { config.coarseStep },
                        set: { config.coarseStep = $0 }
                    ),
                    range: 1...10,
                    description: "Step size for coarse frequency search"
                )
                
                StepperParameter(
                    title: "Fine Search Window",
                    value: Binding(
                        get: { config.fineSearchWindow },
                        set: { config.fineSearchWindow = $0 }
                    ),
                    range: 3...15,
                    description: "Window size for fine frequency search"
                )
            }
        }
    }
    
    // MARK: - Smoothing Section
    
    private var smoothingSection: some View {
        ParameterSection(title: "Smoothing & Stability", icon: "chart.line.uptrend.xyaxis") {
            VStack(spacing: 16) {
                ParameterSlider(
                    title: "Decay Rate",
                    value: Binding(
                        get: { Double(config.decayRate) },
                        set: { config.decayRate = Float($0) }
                    ),
                    range: 0.1...0.99,
                    unit: "",
                    description: "Moving average decay rate (higher = more smoothing)"
                )
                
                StepperParameter(
                    title: "Max Run Length",
                    value: Binding(
                        get: { config.maxRunLength },
                        set: { config.maxRunLength = $0 }
                    ),
                    range: 3...15,
                    description: "Maximum consecutive stable readings"
                )
                
                ParameterSlider(
                    title: "Leap Threshold",
                    value: Binding(
                        get: { Double(config.leapThreshold) },
                        set: { config.leapThreshold = Float($0) }
                    ),
                    range: 0.05...0.5,
                    unit: "",
                    description: "Threshold for detecting frequency jumps"
                )
            }
        }
    }
    
    // MARK: - Performance Section
    
    private var performanceSection: some View {
        ParameterSection(title: "Performance & Buffers", icon: "speedometer") {
            VStack(spacing: 16) {
                ParameterSlider(
                    title: "Buffer Size",
                    value: Binding(
                        get: { Double(config.bufferSize) },
                        set: { config.bufferSize = Float($0) }
                    ),
                    range: 0.05...0.5,
                    unit: "sec",
                    description: "Audio buffer duration"
                )
                
                StepperParameter(
                    title: "Target Buffer Length",
                    value: Binding(
                        get: { config.targetBufferLength },
                        set: { config.targetBufferLength = $0 }
                    ),
                    range: 100...1000,
                    description: "Target length for processing buffer"
                )
                
                if let downsampleFactor = config.downsampleFactorOverride {
                    StepperParameter(
                        title: "Downsample Factor",
                        value: Binding(
                            get: { downsampleFactor },
                            set: { config.downsampleFactorOverride = $0 }
                        ),
                        range: 10...100,
                        description: "Audio downsampling factor"
                    )
                }
            }
        }
    }
    
    // MARK: - Validation Section
    
    private var validationSection: some View {
        ParameterSection(title: "Validation Ranges", icon: "checkmark.shield") {
            VStack(spacing: 16) {
                ParameterSlider(
                    title: "Min Valid Frequency",
                    value: Binding(
                        get: { Double(config.minValidFreq) },
                        set: { config.minValidFreq = Float($0) }
                    ),
                    range: 5...15,
                    unit: "Hz",
                    description: "Minimum valid frequency output"
                )
                
                ParameterSlider(
                    title: "Max Valid Frequency",
                    value: Binding(
                        get: { Double(config.maxValidFreq) },
                        set: { config.maxValidFreq = Float($0) }
                    ),
                    range: 25...60,
                    unit: "Hz",
                    description: "Maximum valid frequency output"
                )
                
                ParameterSlider(
                    title: "Min Valid Pressure",
                    value: Binding(
                        get: { Double(config.minValidPressure) },
                        set: { config.minValidPressure = Float($0) }
                    ),
                    range: 1...15,
                    unit: "cmH2O",
                    description: "Minimum valid pressure output"
                )
                
                ParameterSlider(
                    title: "Max Valid Pressure",
                    value: Binding(
                        get: { Double(config.maxValidPressure) },
                        set: { config.maxValidPressure = Float($0) }
                    ),
                    range: 20...50,
                    unit: "cmH2O",
                    description: "Maximum valid pressure output"
                )
            }
        }
    }
}

#Preview {
    AlgorithmParametersView(configManager: ConfigurationManager())
}
