//
//  BreathDetectionConfig.swift
//  Breath2
//
//  Configurable parameters for breath detection tuning
//

import Foundation

struct BreathDetectionConfig {
    
    // MARK: - Smoothing Parameters
    
    /// Number of samples to use for smoothing (higher = more stable, slower response)
    var smoothingWindowSize: Int = 8
    
    /// Minimum confidence required for state changes (0.0-1.0)
    var confidenceThreshold: Float = 0.65
    
    // MARK: - Hysteresis Thresholds
    
    /// Audio level required to START breath detection
    var startAudioThreshold: Float = 0.010
    
    /// Audio level required to CONTINUE breath detection (lower = less sensitive to gaps)
    var continueAudioThreshold: Float = 0.004
    
    /// Pressure required to START breath detection (cm H2O)
    var startPressureThreshold: Float = 4.5
    
    /// Pressure required to CONTINUE breath detection (cm H2O)
    var continuePressureThreshold: Float = 2.5
    
    // MARK: - Timing Parameters
    
    /// Minimum duration to count as valid breath (seconds)
    var minimumBreathDuration: TimeInterval = 1.0
    
    /// Maximum gap in signal before considering breath ended (seconds)
    var maxSilenceGap: TimeInterval = 0.6
    
    /// Minimum time between separate breaths (seconds)
    var minimumBreathInterval: TimeInterval = 0.4
    
    /// Time to wait before confirming breath end (seconds)
    var confirmationDelay: TimeInterval = 0.3
    
    // MARK: - Signal Processing
    
    /// Weight given to audio signal in combined signal strength (0.0-1.0)
    var audioWeight: Float = 0.4
    
    /// Weight given to pressure signal in combined signal strength (0.0-1.0)
    var pressureWeight: Float = 0.4
    
    /// Weight given to frequency signal in combined signal strength (0.0-1.0)
    var frequencyWeight: Float = 0.2
    
    /// Maximum expected audio level for normalization
    var maxExpectedAudioLevel: Float = 0.05
    
    /// Maximum expected pressure for normalization (cm H2O)
    var maxExpectedPressure: Float = 25.0
    
    /// Maximum expected frequency for normalization (Hz)
    var maxExpectedFrequency: Float = 50.0
    
    // MARK: - Presets
    
    static let sensitive = BreathDetectionConfig(
        smoothingWindowSize: 5,
        confidenceThreshold: 0.5,
        startAudioThreshold: 0.006,
        continueAudioThreshold: 0.002,
        startPressureThreshold: 3.0,
        continuePressureThreshold: 1.5,
        minimumBreathDuration: 0.8,
        maxSilenceGap: 0.4,
        confirmationDelay: 0.2
    )
    
    static let standard = BreathDetectionConfig()
    
    static let stable = BreathDetectionConfig(
        smoothingWindowSize: 12,
        confidenceThreshold: 0.75,
        startAudioThreshold: 0.015,
        continueAudioThreshold: 0.006,
        startPressureThreshold: 5.0,
        continuePressureThreshold: 3.0,
        minimumBreathDuration: 1.2,
        maxSilenceGap: 0.8,
        confirmationDelay: 0.5
    )
    
    // MARK: - Validation
    
    var isValid: Bool {
        return smoothingWindowSize > 0 &&
               confidenceThreshold >= 0.0 && confidenceThreshold <= 1.0 &&
               startAudioThreshold > continueAudioThreshold &&
               startPressureThreshold > continuePressureThreshold &&
               minimumBreathDuration > 0 &&
               maxSilenceGap > 0 &&
               confirmationDelay > 0 &&
               audioWeight + pressureWeight + frequencyWeight <= 1.1 // Allow small rounding
    }
}

// MARK: - Configuration Extensions

extension BreathDetectionConfig {
    
    /// Create a custom configuration for specific use cases
    static func custom(
        sensitivity: Float = 0.5,  // 0.0 = very stable, 1.0 = very sensitive
        responsiveness: Float = 0.5  // 0.0 = slow response, 1.0 = fast response
    ) -> BreathDetectionConfig {
        
        let baseSensitivity = Double(1.0 - sensitivity)
        let baseResponsiveness = Double(1.0 - responsiveness)
        
        return BreathDetectionConfig(
            smoothingWindowSize: Int(5 + baseResponsiveness * 10), // 5-15
            confidenceThreshold: Float(0.4 + baseSensitivity * 0.4), // 0.4-0.8
            startAudioThreshold: Float(0.005 + baseSensitivity * 0.015), // 0.005-0.020
            continueAudioThreshold: Float(0.002 + baseSensitivity * 0.008), // 0.002-0.010
            startPressureThreshold: Float(2.0 + baseSensitivity * 4.0), // 2.0-6.0
            continuePressureThreshold: Float(1.0 + baseSensitivity * 2.0), // 1.0-3.0
            minimumBreathDuration: 0.6 + baseSensitivity * 0.8, // 0.6-1.4
            maxSilenceGap: 0.3 + baseResponsiveness * 0.6, // 0.3-0.9
            confirmationDelay: 0.1 + baseResponsiveness * 0.4 // 0.1-0.5
        )
    }
}