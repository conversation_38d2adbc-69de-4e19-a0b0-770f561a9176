//
//  SessionHistoryManager.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import Foundation
import SwiftUI

// MARK: - Seeded Random Number Generator

/// A deterministic random number generator that produces consistent results for the same seed
struct SeededRandomNumberGenerator: RandomNumberGenerator {
    private var state: UInt64

    init(seed: UInt64) {
        self.state = seed
    }

    mutating func next() -> UInt64 {
        // Linear congruential generator (LCG) algorithm
        state = state &* 1103515245 &+ 12345
        return state
    }

    mutating func nextDouble(in range: ClosedRange<Double>) -> Double {
        let randomValue = Double(next()) / Double(UInt64.max)
        return range.lowerBound + randomValue * (range.upperBound - range.lowerBound)
    }
}

// MARK: - Session History Models

/// **A completed therapy session** containing all session data and metrics.
///
/// This structure stores comprehensive information about a completed PEP therapy session,
/// including timing, quality metrics, pressure readings, and calculated performance indicators.
struct CompletedSession: Codable, Identifiable {
    /// **Unique identifier** for the session
    let id: UUID
    /// **Date and time** when the session was completed
    let date: Date
    /// **Total duration** of the session in seconds
    let duration: TimeInterval
    /// **Overall quality rating** based on performance metrics
    let quality: SessionQuality
    /// **Number of exhalation steps** actually completed
    let stepsCompleted: Int
    /// **Total number of steps** planned for the session
    let totalSteps: Int
    /// **Average pressure** maintained during the session (cm H₂O)
    let averagePressure: Double
    /// **Maximum pressure** reached during the session (cm H₂O)
    let maxPressure: Double
    /// **Minimum pressure** recorded during the session (cm H₂O)
    let minPressure: Double
    /// **Individual pressure readings** collected throughout the session
    let pressureReadings: [StoredPressureReading]
    /// **Session feedback** message for the user
    let feedback: String
    /// **Actual individual breath durations** recorded during the session (in seconds)
    let actualBreathDurations: [Double]

    /// **Creates** a completed session from a therapy session.
    ///
    /// - Parameter therapySession: The completed therapy session to convert
    init(from therapySession: TherapySession) {
        self.id = UUID()
        self.date = Date()
        self.duration = therapySession.sessionDuration
        self.quality = therapySession.sessionQuality
        self.stepsCompleted = therapySession.currentStep
        self.totalSteps = therapySession.totalSteps

        // Calculate pressure statistics
        let pressures = therapySession.pressureReadings.map { $0.pressure }
        self.averagePressure = pressures.isEmpty ? 0.0 : pressures.reduce(0, +) / Double(pressures.count)
        self.maxPressure = pressures.max() ?? 0.0
        self.minPressure = pressures.min() ?? 0.0

        // Convert pressure readings
        self.pressureReadings = therapySession.pressureReadings.map { reading in
            StoredPressureReading(
                timestamp: reading.timestamp,
                pressure: reading.pressure,
                step: reading.step
            )
        }

        self.feedback = therapySession.feedback

        // Store actual breath durations from the therapy session
        self.actualBreathDurations = therapySession.recordedBreathDurations
    }

    /// **Creates** a completed session with explicit parameters for testing or manual creation.
    ///
    /// - Parameters:
    ///   - date: When the session was completed
    ///   - duration: Total session duration in seconds
    ///   - quality: Overall session quality rating
    ///   - stepsCompleted: Number of exhalation steps completed
    ///   - totalSteps: Total planned steps for the session
    ///   - averagePressure: Average pressure maintained (cm H₂O)
    ///   - maxPressure: Maximum pressure reached (cm H₂O)
    ///   - minPressure: Minimum pressure recorded (cm H₂O)
    ///   - pressureReadings: Individual pressure measurements
    ///   - feedback: User feedback message
    ///   - actualBreathDurations: Actual individual breath durations (optional, will generate estimated if nil)
    init(date: Date, duration: TimeInterval, quality: SessionQuality, stepsCompleted: Int, totalSteps: Int, averagePressure: Double, maxPressure: Double, minPressure: Double, pressureReadings: [StoredPressureReading], feedback: String, actualBreathDurations: [Double]? = nil) {
        self.id = UUID()
        self.date = date
        self.duration = duration
        self.quality = quality
        self.stepsCompleted = stepsCompleted
        self.totalSteps = totalSteps
        self.averagePressure = averagePressure
        self.maxPressure = maxPressure
        self.minPressure = minPressure
        self.pressureReadings = pressureReadings
        self.feedback = feedback

        // Use provided breath durations or generate estimated ones for backward compatibility
        if let actualDurations = actualBreathDurations, !actualDurations.isEmpty {
            self.actualBreathDurations = actualDurations
        } else {
            // Generate estimated durations for backward compatibility
            self.actualBreathDurations = Self.generateEstimatedDurations(
                stepsCompleted: stepsCompleted,
                totalDuration: duration,
                sessionId: UUID() // Use a temporary UUID for estimation
            )
        }
    }
    
    // Computed properties for display
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    var completionPercentage: Double {
        return Double(stepsCompleted) / Double(totalSteps)
    }
    
    var qualityIcon: String {
        switch quality {
        case .excellent: return "star.fill"
        case .good: return "checkmark.circle.fill"
        case .fair: return "minus.circle.fill"
        case .poor: return "xmark.circle.fill"
        case .none: return "questionmark.circle.fill"
        }
    }
    
    var qualityColor: Color {
        switch quality {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .orange
        case .poor: return .red
        case .none: return .gray
        }
    }

    // MARK: - Enhanced Metrics for Refined UI

    /// Percentage of exhalations performed in the green zone (10-20 cm H₂O)
    var greenZonePercentage: Double {
        guard !pressureReadings.isEmpty else { return 0.0 }
        let greenReadings = pressureReadings.filter { reading in
            reading.pressure >= 10.0 && reading.pressure <= 20.0
        }
        return Double(greenReadings.count) / Double(pressureReadings.count) * 100
    }

    /// Average duration per exhalation in seconds
    var averageExhalationDuration: Double {
        guard stepsCompleted > 0 else { return 0.0 }
        return duration / Double(stepsCompleted)
    }

    /// The predominant exhalation effort zone during the session
    var predominantEffortZone: ExhalationEffortZone {
        guard !pressureReadings.isEmpty else { return .amber }

        let greenCount = pressureReadings.filter { $0.pressure >= 10.0 && $0.pressure <= 20.0 }.count
        let redCount = pressureReadings.filter { $0.pressure > 20.0 }.count
        let amberCount = pressureReadings.count - greenCount - redCount

        let maxCount = max(greenCount, max(amberCount, redCount))

        if maxCount == greenCount {
            return .green
        } else if maxCount == redCount {
            return .red
        } else {
            return .amber
        }
    }

    /// Zone consistency score (0-100) indicating how consistently the user stayed in target zones
    var zoneConsistencyScore: Double {
        guard !pressureReadings.isEmpty else { return 0.0 }

        // Calculate time spent in green zone as primary metric
        let greenZoneTime = greenZonePercentage

        // Bonus points for avoiding red zone
        let redReadings = pressureReadings.filter { $0.pressure > 20.0 }
        let redZonePercentage = Double(redReadings.count) / Double(pressureReadings.count) * 100
        let redZonePenalty = min(redZonePercentage * 0.5, 20.0) // Max 20 point penalty

        return max(0.0, min(100.0, greenZoneTime - redZonePenalty))
    }

    /// Individual exhalation durations for the new time graph
    /// Now returns ACTUAL breath durations when available, or estimated durations for backward compatibility
    var exhalationDurations: [Double] {
        guard stepsCompleted > 0 else { return [] }

        // Return actual breath durations if available and matches expected count
        if !actualBreathDurations.isEmpty && actualBreathDurations.count <= stepsCompleted {
            // If we have fewer actual durations than steps completed, pad with estimated durations
            if actualBreathDurations.count < stepsCompleted {
                let remainingSteps = stepsCompleted - actualBreathDurations.count
                let avgDuration = actualBreathDurations.reduce(0, +) / Double(actualBreathDurations.count)
                let estimatedDurations = Self.generateEstimatedDurations(
                    stepsCompleted: remainingSteps,
                    totalDuration: TimeInterval(remainingSteps) * avgDuration,
                    sessionId: id
                )
                return actualBreathDurations + estimatedDurations
            }
            return Array(actualBreathDurations.prefix(stepsCompleted))
        }

        // Fallback to estimated durations for backward compatibility
        return Self.generateEstimatedDurations(
            stepsCompleted: stepsCompleted,
            totalDuration: duration,
            sessionId: id
        )
    }

    /// Generate estimated breath durations with realistic variation (for backward compatibility)
    private static func generateEstimatedDurations(stepsCompleted: Int, totalDuration: TimeInterval, sessionId: UUID) -> [Double] {
        guard stepsCompleted > 0 else { return [] }

        let avgDuration = totalDuration / Double(stepsCompleted)
        let seed = abs(sessionId.hashValue)

        return Array(1...stepsCompleted).map { stepIndex in
            let stepSeed = seed + stepIndex
            var stepGenerator = SeededRandomNumberGenerator(seed: UInt64(stepSeed))
            let variation = stepGenerator.nextDouble(in: -0.5...0.5)
            return max(1.0, avgDuration + variation)
        }
    }
}

// MARK: - Exhalation Effort Zone

enum ExhalationEffortZone: String, CaseIterable {
    case green = "In Zone"
    case amber = "Below Zone"
    case red = "Above Zone"

    var color: Color {
        switch self {
        case .green: return .green
        case .amber: return .orange
        case .red: return .red
        }
    }

    var description: String {
        switch self {
        case .green: return "Optimal exhalation effort"
        case .amber: return "Needs more effort"
        case .red: return "Too much effort"
        }
    }
}

struct StoredPressureReading: Codable {
    let timestamp: Date
    let pressure: Double
    let step: Int
}

// MARK: - Session History Manager

/// **Manages** the storage and retrieval of completed therapy sessions.
///
/// This class handles persistent storage of session history using file-based storage
/// with automatic data management, provides statistical calculations, and manages
/// session data lifecycle. It automatically loads recent sessions on initialization
/// and provides methods for saving, deleting, and analyzing session data.
///
/// **Storage Strategy:**
/// - Recent sessions (last 30 days) are kept in memory for fast access
/// - Older sessions are stored in files and loaded on demand
/// - Automatic cleanup of very old data (>1 year) to prevent storage bloat
class SessionHistoryManager: ObservableObject {
    /// **Recent completed sessions** (last 30 days) ordered by date (newest first)
    @Published var sessions: [CompletedSession] = []

    // MARK: - Storage Configuration
    private let maxInMemorySessions = 100 // Maximum sessions to keep in memory
    private let maxStorageDays = 365 // Keep data for 1 year maximum
    private let documentsDirectory: URL
    private let sessionsDirectory: URL

    /// **Creates** a session history manager and sets up file-based storage.
    init() {
        // Set up storage directories
        documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        sessionsDirectory = documentsDirectory.appendingPathComponent("TherapySessions")

        // Create sessions directory if it doesn't exist
        try? FileManager.default.createDirectory(at: sessionsDirectory, withIntermediateDirectories: true)

        // Load recent sessions and perform maintenance
        loadRecentSessions()
        performMaintenanceTasks()
    }

    /// **Cleans up** resources when deallocated.
    deinit {
        // Save any pending changes
        saveRecentSessions()
        print("🧹 SessionHistoryManager deallocated")
    }
    
    // MARK: - Public Methods
    
    /// **Saves** a completed therapy session to history.
    ///
    /// - Parameter therapySession: The therapy session to save
    func saveSession(_ therapySession: TherapySession) {
        guard therapySession.sessionDuration > 0 else { return }

        let completedSession = CompletedSession(from: therapySession)

        sessions.insert(completedSession, at: 0) // Add to beginning for newest first

        // Keep only recent sessions in memory
        if sessions.count > maxInMemorySessions {
            archiveOlderSessions()
        }

        saveRecentSessions()

        print("✅ Session saved to history: \(completedSession.formattedDuration), Quality: \(completedSession.quality)")
    }
    
    /// **Deletes** a specific session from history.
    ///
    /// - Parameter session: The session to delete
    func deleteSession(_ session: CompletedSession) {
        sessions.removeAll { $0.id == session.id }
        saveRecentSessions()

        // Also remove from archived files if it exists
        removeFromArchive(sessionId: session.id)
    }

    /// **Clears** all session history.
    func clearAllSessions() {
        sessions.removeAll()
        saveRecentSessions()

        // Clear all archived files
        clearAllArchives()
    }
    
    // MARK: - Statistics
    
    var totalSessions: Int {
        sessions.count
    }
    
    var totalTherapyTime: TimeInterval {
        sessions.reduce(0) { $0 + $1.duration }
    }
    
    var averageSessionDuration: TimeInterval {
        guard !sessions.isEmpty else { return 0 }
        return totalTherapyTime / Double(sessions.count)
    }
    
    var excellentSessionsCount: Int {
        sessions.filter { $0.quality == .excellent }.count
    }
    
    var thisWeekSessions: [CompletedSession] {
        let calendar = Calendar.current
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        return sessions.filter { $0.date >= weekAgo }
    }
    
    var thisMonthSessions: [CompletedSession] {
        let calendar = Calendar.current
        let monthAgo = calendar.date(byAdding: .month, value: -1, to: Date()) ?? Date()
        return sessions.filter { $0.date >= monthAgo }
    }

    var todaySessions: [CompletedSession] {
        let calendar = Calendar.current
        return sessions.filter { calendar.isDate($0.date, inSameDayAs: Date()) }
    }

    var todaySessionsCount: Int {
        return todaySessions.count
    }
    
    // MARK: - Private Storage Methods

    /// **Saves** recent sessions to a file for fast loading.
    private func saveRecentSessions() {
        let recentSessionsFile = sessionsDirectory.appendingPathComponent("recent_sessions.json")

        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(sessions)
            try data.write(to: recentSessionsFile)
            print("💾 Saved \(sessions.count) recent sessions to file")
        } catch {
            print("❌ Failed to save recent sessions: \(error)")
        }
    }

    /// **Loads** recent sessions from file storage.
    private func loadRecentSessions() {
        let recentSessionsFile = sessionsDirectory.appendingPathComponent("recent_sessions.json")

        guard FileManager.default.fileExists(atPath: recentSessionsFile.path) else {
            print("📱 No recent sessions file found")
            // Try to migrate from UserDefaults if it exists
            migrateFromUserDefaults()
            return
        }

        do {
            let data = try Data(contentsOf: recentSessionsFile)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            sessions = try decoder.decode([CompletedSession].self, from: data)

            // Keep only recent sessions in memory
            let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
            sessions = sessions.filter { $0.date >= thirtyDaysAgo }

            print("✅ Loaded \(sessions.count) recent sessions from file")
        } catch {
            print("❌ Failed to load recent sessions: \(error)")
            sessions = []
        }
    }

    /// **Migrates** data from UserDefaults to file-based storage.
    private func migrateFromUserDefaults() {
        let userDefaults = UserDefaults.standard
        let sessionsKey = "therapy_sessions"

        guard let data = userDefaults.data(forKey: sessionsKey) else {
            print("📱 No UserDefaults data to migrate")
            return
        }

        do {
            let decoder = JSONDecoder()
            let oldSessions = try decoder.decode([CompletedSession].self, from: data)
            sessions = oldSessions

            // Save to new file-based storage
            saveRecentSessions()

            // Archive older sessions
            archiveOlderSessions()

            // Remove from UserDefaults
            userDefaults.removeObject(forKey: sessionsKey)

            print("✅ Migrated \(oldSessions.count) sessions from UserDefaults to file storage")
        } catch {
            print("❌ Failed to migrate from UserDefaults: \(error)")
        }
    }

    // MARK: - Archive Management

    /// **Archives** older sessions to separate files to keep memory usage low.
    private func archiveOlderSessions() {
        let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        let sessionsToArchive = sessions.filter { $0.date < thirtyDaysAgo }

        guard !sessionsToArchive.isEmpty else { return }

        // Group sessions by month for efficient storage
        let groupedSessions = Dictionary(grouping: sessionsToArchive) { session in
            Calendar.current.dateInterval(of: .month, for: session.date)?.start ?? session.date
        }

        for (monthStart, monthSessions) in groupedSessions {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM"
            let monthString = formatter.string(from: monthStart)
            let archiveFile = sessionsDirectory.appendingPathComponent("archive_\(monthString).json")

            do {
                // Load existing archive if it exists
                var existingSessions: [CompletedSession] = []
                if FileManager.default.fileExists(atPath: archiveFile.path) {
                    let existingData = try Data(contentsOf: archiveFile)
                    let decoder = JSONDecoder()
                    decoder.dateDecodingStrategy = .iso8601
                    existingSessions = try decoder.decode([CompletedSession].self, from: existingData)
                }

                // Merge with new sessions
                let allSessions = (existingSessions + monthSessions).sorted { $0.date > $1.date }

                // Save archive
                let encoder = JSONEncoder()
                encoder.dateEncodingStrategy = .iso8601
                let data = try encoder.encode(allSessions)
                try data.write(to: archiveFile)

                print("📦 Archived \(monthSessions.count) sessions for \(monthString)")
            } catch {
                print("❌ Failed to archive sessions for \(monthString): \(error)")
            }
        }

        // Remove archived sessions from memory
        sessions.removeAll { session in
            sessionsToArchive.contains { $0.id == session.id }
        }
    }

    /// **Performs** maintenance tasks like cleaning up old data.
    private func performMaintenanceTasks() {
        DispatchQueue.global(qos: .utility).async { [weak self] in
            self?.cleanupOldData()
        }
    }

    /// **Cleans up** data older than the maximum storage period.
    private func cleanupOldData() {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -maxStorageDays, to: Date()) ?? Date()
        let fileManager = FileManager.default

        do {
            let archiveFiles = try fileManager.contentsOfDirectory(at: sessionsDirectory, includingPropertiesForKeys: nil)
                .filter { $0.pathExtension == "json" && $0.lastPathComponent.hasPrefix("archive_") }

            for archiveFile in archiveFiles {
                // Extract date from filename (format: archive_yyyy-MM.json)
                let filename = archiveFile.lastPathComponent
                let dateString = String(filename.dropFirst(8).dropLast(5)) // Remove "archive_" and ".json"

                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM"

                if let archiveDate = formatter.date(from: dateString), archiveDate < cutoffDate {
                    try fileManager.removeItem(at: archiveFile)
                    print("🗑️ Deleted old archive: \(filename)")
                }
            }
        } catch {
            print("❌ Failed to cleanup old data: \(error)")
        }
    }

    /// **Removes** a session from archived files.
    private func removeFromArchive(sessionId: UUID) {
        // This is a simplified implementation - in practice, you might want to
        // load each archive file and remove the session if found
        print("🗑️ Session \(sessionId) removal from archives not implemented")
    }

    /// **Clears** all archived session files.
    private func clearAllArchives() {
        do {
            let archiveFiles = try FileManager.default.contentsOfDirectory(at: sessionsDirectory, includingPropertiesForKeys: nil)
                .filter { $0.pathExtension == "json" }

            for archiveFile in archiveFiles {
                try FileManager.default.removeItem(at: archiveFile)
            }

            print("🗑️ Cleared all session archives")
        } catch {
            print("❌ Failed to clear archives: \(error)")
        }
    }
}


