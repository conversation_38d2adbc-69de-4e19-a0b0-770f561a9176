//
//  TherapySession.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//

import Foundation
import SwiftUI

// MARK: - Therapy Session Model

class TherapySession: ObservableObject {
    @Published var currentStep: Int = 1
    @Published var totalSteps: Int = 10
    @Published var currentPressure: Double = 0.0
    @Published var targetPressure: Double = 15.0 // Target PEP pressure
    @Published var sessionProgress: Double = 0.0
    @Published var isActive: Bool = false
    @Published var sessionDuration: TimeInterval = 0
    @Published var feedback: String = "Ready to begin"
    @Published var sessionQuality: SessionQuality = .none

    // Configuration integration
    @Published var configuration: TherapyConfiguration
    @Published var currentBlockName: String = ""
    
    // Session tracking
    private var sessionStartTime: Date?
    private var sessionTimer: Timer?
    
    // Pressure history for this session
    @Published var pressureReadings: [PressureReading] = []

    // Step performance tracking
    @Published var stepPerformances: [Int: StepPerformance] = [:]
    private var lastBreathEndTime: Date?

    // Breath duration tracking - stores actual individual breath durations
    @Published var recordedBreathDurations: [Double] = []

    init(configuration: TherapyConfiguration = TherapyConfiguration()) {
        self.configuration = configuration
        setupCurrentSession()
        updateProgress()
        initializeStepPerformances()
    }

    /// **Cleans up** session resources when deallocated.
    deinit {
        sessionTimer?.invalidate()
        sessionTimer = nil
        print("🧹 TherapySession deallocated - timer cleaned up")
    }

    private func setupCurrentSession() {
        configuration.checkAndResetIfNewDay()

        if let currentBlock = configuration.currentBlock {
            totalSteps = currentBlock.exhalations
            currentBlockName = currentBlock.name
        } else {
            // No more sessions today or configuration is empty
            totalSteps = 10
            currentBlockName = "Session Complete"
        }
    }

    private func initializeStepPerformances() {
        stepPerformances.removeAll()
        for step in 1...totalSteps {
            stepPerformances[step] = .notStarted
        }
    }
    
    // MARK: - Session Control
    
    func startSession() {
        // Safety check - don't start if already active
        guard !isActive else {
            print("⚠️ Session already active, ignoring start request")
            return
        }

        isActive = true
        sessionStartTime = Date()
        currentStep = 1
        sessionProgress = 0.0
        pressureReadings.removeAll()
        sessionQuality = .none
        feedback = "Session started - Begin breathing into device"

        // Reset step performances and breath tracking
        initializeStepPerformances()
        lastBreathEndTime = nil
        recordedBreathDurations.removeAll()

        // Start timer for session duration
        sessionTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            self.updateSessionDuration()
        }

        print("✅ New therapy session started")
    }
    
    func pauseSession() {
        isActive = false
        sessionTimer?.invalidate()
        feedback = "Session paused"
    }
    
    func resumeSession() {
        isActive = true
        sessionTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            self.updateSessionDuration()
        }
        feedback = "Session resumed"
    }
    
    func endSession() {
        isActive = false
        sessionTimer?.invalidate()
        sessionTimer = nil

        // Calculate session quality
        calculateSessionQuality()

        // Mark current block as completed if session was finished
        if currentStep >= totalSteps {
            configuration.completeCurrentSession()
        }

        feedback = getSessionSummary()
    }

    func resetSession() {
        isActive = false
        sessionTimer?.invalidate()
        sessionTimer = nil
        currentStep = 1
        sessionProgress = 0.0
        sessionDuration = 0
        pressureReadings.removeAll()
        sessionQuality = .none
        sessionStartTime = nil

        // Reset step performances
        initializeStepPerformances()
        lastBreathEndTime = nil

        // Setup current session based on configuration
        setupCurrentSession()
        feedback = configuration.hasMoreSessions ? "Ready to begin \(currentBlockName)" : "All sessions completed today"

        updateProgress()
    }

    // MARK: - Session Navigation

    func startNextSession() {
        guard configuration.hasMoreSessions else { return }

        resetSession()
        startSession()
    }

    var hasMoreSessionsToday: Bool {
        return configuration.hasMoreSessions
    }

    var dailyProgress: Double {
        return configuration.progressToday
    }

    var sessionsCompletedToday: Int {
        return configuration.sessionsCompletedToday
    }

    var totalSessionsToday: Int {
        return configuration.dailyBlocks.count
    }
    
    func nextStep() {
        if currentStep < totalSteps {
            currentStep += 1
            updateProgress()
            feedback = "Step \(currentStep) of \(totalSteps) - Keep breathing steadily"
        } else {
            endSession()
        }
    }

    func previousStep() {
        if currentStep > 1 {
            currentStep -= 1
            updateProgress()
            feedback = "Step \(currentStep) of \(totalSteps) - Continue breathing"
        }
    }
    
    // MARK: - Pressure Monitoring
    
    func updatePressure(_ pressure: Double) {
        currentPressure = pressure

        // Add to readings history
        let reading = PressureReading(
            timestamp: Date(),
            pressure: pressure,
            step: currentStep
        )
        pressureReadings.append(reading)

        // Update feedback based on pressure
        updatePressureFeedback()

        // Auto-advance if pressure is maintained in target range
        checkAutoAdvance()
    }

    // MARK: - Breath Performance Tracking

    func processCompletedBreath(_ breathEvent: BreathEvent) {
        // Only process if this step hasn't been completed yet
        guard stepPerformances[currentStep]?.isCompleted != true else {
            print("🫁 Step \(currentStep) already completed, ignoring breath")
            return
        }

        // Prevent duplicate processing of the same breath
        if let lastEnd = lastBreathEndTime,
           abs(breathEvent.endTime.timeIntervalSince(lastEnd)) < 0.5 {
            print("🫁 Ignoring duplicate breath event")
            return
        }

        let duration = breathEvent.duration
        let stepToUpdate = currentStep

        // Record the actual breath duration for accurate charting
        recordedBreathDurations.append(duration)
        print("🫁 Recorded breath duration: \(String(format: "%.1f", duration))s (Total: \(recordedBreathDurations.count))")

        // Calculate performance based on the breath event and pressure history
        let performance = calculateBreathPerformance(
            duration: duration,
            startTime: breathEvent.startTime,
            endTime: breathEvent.endTime
        )

        // Update step performance
        stepPerformances[stepToUpdate] = performance
        lastBreathEndTime = breathEvent.endTime

        print("🫁 Breath completed for step \(stepToUpdate): duration=\(String(format: "%.1f", duration))s, performance=\(performance)")

        // Move to next step if completed
        if performance.isCompleted && stepToUpdate < totalSteps {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                guard let self = self else { return }
                if self.currentStep == stepToUpdate && self.currentStep < self.totalSteps {
                    self.nextStep()
                }
            }
        }
    }

    private func calculateBreathPerformance(duration: TimeInterval, startTime: Date, endTime: Date) -> StepPerformance {
        // Check duration first
        if duration < 1.5 {
            return .inProgress // Too short, not a valid breath
        } else if duration >= 1.5 && duration < 3.0 {
            return .completedAmber // Short breath (1.5-3 seconds) = amber
        }

        // For breaths 3+ seconds, check pressure zones
        let breathReadings = pressureReadings.filter { reading in
            reading.timestamp >= startTime && reading.timestamp <= endTime
        }

        guard !breathReadings.isEmpty else { return .completedGreen }

        // Calculate time in amber and red zones only
        var timeInAmberZone: TimeInterval = 0
        var timeInRedZone: TimeInterval = 0

        for reading in breathReadings {
            let timeIncrement = 0.2 // Assuming ~5 readings per second

            if reading.pressure >= 10.0 && reading.pressure <= 20.0 {
                // Green zone (target range) - no need to track, this is default
                continue
            } else if reading.pressure > 20.0 {
                // Red zone (too high pressure)
                timeInRedZone += timeIncrement
            } else {
                // Amber zone (too low or slightly high pressure)
                timeInAmberZone += timeIncrement
            }
        }

        // Simple logic: Green by default, unless user spent 3+ seconds in amber or red zones
        if timeInRedZone >= 3.0 {
            return .completedRed
        } else if timeInAmberZone >= 3.0 {
            return .completedAmber
        } else {
            return .completedGreen // Default for good breaths
        }
    }

    private func updatePressureFeedback() {
        let pressure = currentPressure

        if pressure < 5.0 {
            feedback = "Blow harder into the device"
            sessionQuality = .poor
        } else if pressure < 8.0 {
            feedback = "Increase pressure - blow harder"
            sessionQuality = .poor
        } else if pressure < 10.0 {
            feedback = "Almost there - a bit more pressure"
            sessionQuality = .fair
        } else if pressure >= 10.0 && pressure <= 20.0 {
            feedback = "Perfect! Maintain this pressure"
            sessionQuality = .excellent
        } else if pressure <= 25.0 {
            feedback = "Too high - breathe more gently"
            sessionQuality = .fair
        } else {
            feedback = "Much too high - reduce pressure"
            sessionQuality = .poor
        }
    }
    
    private func checkAutoAdvance() {
        // Auto-advance if pressure is in target range for sufficient time
        let recentReadings = pressureReadings.suffix(15) // Last 15 readings (about 3 seconds)
        let inTargetRange = recentReadings.allSatisfy { reading in
            reading.pressure >= 10.0 && reading.pressure <= 20.0
        }

        if inTargetRange && recentReadings.count >= 15 && isActive && currentStep < totalSteps {
            // Advance to next step after maintaining good pressure for 3 seconds
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                guard let self = self else { return }
                if self.isActive && self.sessionQuality == .excellent && self.currentStep < self.totalSteps {
                    self.nextStep()
                }
            }
        }
    }
    
    // MARK: - Progress Calculation
    
    private func updateProgress() {
        sessionProgress = Double(currentStep - 1) / Double(totalSteps - 1)
    }
    
    private func updateSessionDuration() {
        guard let startTime = sessionStartTime else { return }
        sessionDuration = Date().timeIntervalSince(startTime)
    }
    
    private func calculateSessionQuality() {
        guard !pressureReadings.isEmpty else {
            sessionQuality = .none
            return
        }
        
        let inTargetCount = pressureReadings.filter { reading in
            reading.pressure >= 10.0 && reading.pressure <= 20.0
        }.count
        
        let percentage = Double(inTargetCount) / Double(pressureReadings.count)
        
        if percentage >= 0.8 {
            sessionQuality = .excellent
        } else if percentage >= 0.6 {
            sessionQuality = .good
        } else if percentage >= 0.4 {
            sessionQuality = .fair
        } else {
            sessionQuality = .poor
        }
    }
    
    private func getSessionSummary() -> String {
        let minutes = Int(sessionDuration) / 60
        let seconds = Int(sessionDuration) % 60
        
        switch sessionQuality {
        case .excellent:
            return "Excellent session! Duration: \(minutes)m \(seconds)s"
        case .good:
            return "Good session! Duration: \(minutes)m \(seconds)s"
        case .fair:
            return "Fair session. Duration: \(minutes)m \(seconds)s"
        case .poor:
            return "Session completed. Duration: \(minutes)m \(seconds)s"
        case .none:
            return "Session ended"
        }
    }
}

// MARK: - Supporting Models

struct PressureReading {
    let timestamp: Date
    let pressure: Double
    let step: Int
}

// MARK: - Step Performance

enum StepPerformance {
    case notStarted
    case inProgress
    case completedGreen    // Default: 3+ seconds, mostly in green zone
    case completedAmber    // 1.5-3 seconds OR 3+ seconds with 3+ seconds in amber zone
    case completedRed      // 3+ seconds with 3+ seconds in red zone

    var color: Color {
        switch self {
        case .notStarted: return .gray.opacity(0.3)
        case .inProgress: return .blue
        case .completedGreen: return .green
        case .completedAmber: return .orange
        case .completedRed: return .red
        }
    }

    var isCompleted: Bool {
        switch self {
        case .completedGreen, .completedAmber, .completedRed:
            return true
        default:
            return false
        }
    }
}

// MARK: - Breath Performance Data

struct BreathPerformanceData {
    let stepNumber: Int
    let startTime: Date
    var endTime: Date?
    var duration: TimeInterval = 0
    var timeInGreenZone: TimeInterval = 0
    var timeInAmberZone: TimeInterval = 0
    var timeInRedZone: TimeInterval = 0
    var averagePressure: Double = 0
    var pressureReadings: [Double] = []
}

enum SessionQuality: Codable {
    case none
    case poor
    case fair
    case good
    case excellent

    var color: Color {
        switch self {
        case .none: return .gray
        case .poor: return .red
        case .fair: return .orange
        case .good: return .blue
        case .excellent: return .green
        }
    }
    
    var description: String {
        switch self {
        case .none: return "Not Started"
        case .poor: return "Needs Improvement"
        case .fair: return "Fair"
        case .good: return "Good"
        case .excellent: return "Excellent"
        }
    }
}
