//
//  PressureCalculator.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Implements the linear model to convert detected pitch to pressure (PEP)
//  Based on the correlation established in the Acapella paper (r² = 0.886)

import Foundation

class PressureCalculator {
    
    // MARK: - Linear Model Parameters
    // Based on the paper's findings: strong linear correlation between pitch and pressure
    // The paper shows r² = 0.886 across 9,993 data points
    // From the calculations image, the relationship appears to be:
    // Pressure = slope * Pitch + intercept
    
    private let slope: Float
    private let intercept: Float
    
    // MARK: - Validation Parameters
    private let minValidFreq: Float = 7.0  // Hz - extended minimum frequency
    private let maxValidFreq: Float = 40.0  // Hz - maximum frequency from paper
    private let minValidPressure: Float = 6.0   // cm H2O - minimum pressure from paper
    private let maxValidPressure: Float = 30.0  // cm H2O - maximum pressure from paper
    
    // MARK: - Initialization
    init() {
        // Linear model coefficients from the actual research data
        // Equation: Pressure = -4.659 + 1.119 × Pitch
        // r² = 0.886 across 9,993 data points

        self.slope = 1.119  // cm H2O per Hz
        self.intercept = -4.659  // cm H2O baseline

        print("PressureCalculator initialized:")
        print("  Linear model: Pressure = \(intercept) + \(slope) * Frequency")
        print("  Research equation: Pressure = -4.659 + 1.119 × Pitch")
        print("  r² = 0.886 across 9,993 data points")
        print("  Valid frequency range: \(minValidFreq)-\(maxValidFreq) Hz")
        print("  Valid pressure range: \(minValidPressure)-\(maxValidPressure) cm H2O")
    }
    
    // MARK: - Main Calculation Method
    
    /// Calculates pressure from detected pitch using the linear model
    /// - Parameter pitch: Detected frequency in Hz
    /// - Returns: Calculated pressure in cm H2O
    func calculatePressure(fromPitch pitch: Float) -> Float {
        // Validate input frequency
        guard pitch >= minValidFreq && pitch <= maxValidFreq else {
            return 0.0  // Return 0 for invalid frequencies
        }
        
        // Apply linear model: Pressure = slope * Pitch + intercept
        let calculatedPressure = slope * pitch + intercept
        
        // Clamp to valid pressure range
        let clampedPressure = max(0.0, min(calculatedPressure, maxValidPressure))
        
        return clampedPressure
    }
    
    // MARK: - Utility Methods
    
    /// Checks if the detected frequency is within the valid range
    /// - Parameter frequency: Frequency in Hz
    /// - Returns: True if frequency is valid for pressure calculation
    func isValidFrequency(_ frequency: Float) -> Bool {
        return frequency >= minValidFreq && frequency <= maxValidFreq
    }
    
    /// Checks if the calculated pressure is within the target therapeutic range
    /// - Parameter pressure: Pressure in cm H2O
    /// - Returns: True if pressure is in the target range (10-20 cm H2O)
    func isInTargetRange(_ pressure: Float) -> Bool {
        return pressure >= 10.0 && pressure <= 20.0
    }
    
    /// Provides feedback on pressure level relative to target range
    /// - Parameter pressure: Pressure in cm H2O
    /// - Returns: Feedback string for user guidance
    func getPressureFeedback(_ pressure: Float) -> String {
        if pressure < 6.0 {
            return "Too Low - Breathe harder"
        } else if pressure < 10.0 {
            return "Below Target - Increase effort"
        } else if pressure <= 20.0 {
            return "Perfect Range ✓"
        } else if pressure <= 25.0 {
            return "Above Target - Reduce effort"
        } else {
            return "Too High - Breathe gentler"
        }
    }
    
    /// Calculates the confidence level of the pressure reading
    /// Based on how well the frequency fits within the expected range
    /// - Parameter frequency: Detected frequency in Hz
    /// - Returns: Confidence level from 0.0 to 1.0
    func getConfidenceLevel(_ frequency: Float) -> Float {
        guard isValidFrequency(frequency) else {
            return 0.0
        }
        
        // Higher confidence for frequencies in the middle of the range
        let midFreq = (minValidFreq + maxValidFreq) / 2.0
        let freqRange = maxValidFreq - minValidFreq
        let distanceFromMid = abs(frequency - midFreq)
        let normalizedDistance = distanceFromMid / (freqRange / 2.0)
        
        // Confidence decreases as we move away from the center frequency
        return max(0.0, 1.0 - normalizedDistance)
    }
    
    // MARK: - Calibration Methods
    
    /// Updates the linear model parameters based on new calibration data
    /// This could be used for user-specific calibration in future versions
    /// - Parameters:
    ///   - newSlope: New slope coefficient
    ///   - newIntercept: New intercept coefficient
    func updateModel(slope newSlope: Float, intercept newIntercept: Float) {
        // Note: In a production app, you might want to validate these parameters
        // and possibly store them in UserDefaults for persistence
        print("Model update requested - slope: \(newSlope), intercept: \(newIntercept)")
        print("Current implementation uses fixed parameters from research paper")
    }
    
    /// Provides statistics about the current model
    /// - Returns: Dictionary with model information
    func getModelInfo() -> [String: Any] {
        return [
            "slope": slope,
            "intercept": intercept,
            "r_squared": 0.886,  // From the paper
            "data_points": 9993,  // From the paper
            "frequency_range": "\(minValidFreq)-\(maxValidFreq) Hz",
            "pressure_range": "\(minValidPressure)-\(maxValidPressure) cm H2O",
            "target_range": "10-20 cm H2O"
        ]
    }
}
