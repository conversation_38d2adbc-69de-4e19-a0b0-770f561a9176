//
//  SessionDetailView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import SwiftUI
import Charts

struct SessionDetailView: View {
    let session: CompletedSession
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab: DetailTab = .overview
    
    enum DetailTab: String, CaseIterable {
        case overview = "Overview"
        case adherence = "Adherence"
        case effort = "Effort"
        case time = "Time"

        var systemImage: String {
            switch self {
            case .overview: return "chart.bar.doc.horizontal"
            case .adherence: return "checkmark.circle.fill"
            case .effort: return "target"
            case .time: return "clock.fill"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Professional dark background
                LinearGradient(
                    colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),
                        Color(red: 0.08, green: 0.08, blue: 0.12),
                        Color(red: 0.10, green: 0.10, blue: 0.15)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 24) {
                        // Session Header
                        sessionHeaderView
                        
                        // Tab Selector
                        tabSelectorView
                        
                        // Content based on selected tab
                        switch selectedTab {
                        case .overview:
                            overviewContent
                        case .adherence:
                            adherenceContent
                        case .effort:
                            effortContent
                        case .time:
                            timeContent
                        }
                        
                        Spacer(minLength: 50)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 10)
                }
            }
            .navigationTitle("Session Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.cyan)
                }
            }
        }
    }
    
    // MARK: - Session Header
    
    private var sessionHeaderView: some View {
        VStack(spacing: 16) {
            // Date and time
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(session.date, format: .dateTime.weekday(.wide).month().day().year())
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text(session.date, style: .time)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                // Quality badge
                HStack(spacing: 8) {
                    Image(systemName: session.qualityIcon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(session.qualityColor)

                    Text(session.quality.description)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(session.qualityColor)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(session.quality.color.opacity(0.2))
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(session.qualityColor.opacity(0.5), lineWidth: 1)
                        )
                )
            }
            
            // Key metrics
            HStack(spacing: 16) {
                MetricCard(
                    title: "Session Duration",
                    value: session.formattedDuration,
                    icon: "clock.fill",
                    color: .blue
                )

                MetricCard(
                    title: "Exhalations",
                    value: "\(session.stepsCompleted)/\(session.totalSteps)",
                    icon: "lungs.fill",
                    color: .green
                )

                MetricCard(
                    title: "Target Effort",
                    value: "\(Int(session.greenZonePercentage))%",
                    icon: "target",
                    color: .green
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(session.qualityColor.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Tab Selector
    
    private var tabSelectorView: some View {
        HStack(spacing: 0) {
            ForEach(DetailTab.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selectedTab = tab
                    }
                }) {
                    VStack(spacing: 6) {
                        Image(systemName: tab.systemImage)
                            .font(.system(size: 16, weight: .medium))
                        
                        Text(tab.rawValue)
                            .font(.system(size: 12, weight: .medium, design: .rounded))
                    }
                    .foregroundColor(selectedTab == tab ? .white : .gray)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(selectedTab == tab ? Color.cyan : Color.clear)
                    )
                }
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 14)
                .fill(Color.white.opacity(0.05))
        )
    }
    
    // MARK: - Overview Content

    private var overviewContent: some View {
        VStack(spacing: 24) {
            // A. Session At-a-Glance (already in header - no additional content needed)

            // B. Adherence Summary
            adherenceSummaryView

            // C. Exhalation Effort Compliance - Overview
            effortComplianceOverviewView

            // D. Exhalation Time Compliance - Overview
            timeComplianceOverviewView

            // E. Overall Session Feedback
            if !session.feedback.isEmpty {
                feedbackView
            }
        }
    }

    // MARK: - Adherence Content

    private var adherenceContent: some View {
        VStack(spacing: 24) {
            // Adherence Metrics Summary
            adherenceMetricsSummaryView
        }
    }

    // MARK: - B. Adherence Summary

    private var adherenceSummaryView: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Adherence", subtitle: "Session completion status")

            HStack(spacing: 16) {
                // Exhalations Completed (already in header, but more detailed here)
                OverviewMetricCard(
                    title: "Exhalations Completed",
                    value: "\(session.stepsCompleted)/\(session.totalSteps)",
                    subtitle: completionStatusText,
                    icon: "lungs.fill",
                    color: session.stepsCompleted >= session.totalSteps ? .green : .orange
                )

                // Good Exhalations (meeting both effort and time criteria)
                OverviewMetricCard(
                    title: "Quality Exhalations",
                    value: "\(goodExhalationsCount)/\(session.stepsCompleted)",
                    subtitle: "Met both criteria",
                    icon: "checkmark.circle.fill",
                    color: goodExhalationsPercentage >= 80 ? .green : goodExhalationsPercentage >= 60 ? .orange : .red
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
        )
    }

    // MARK: - C. Exhalation Effort Compliance - Overview

    private var effortComplianceOverviewView: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Effort Overview", subtitle: "Exhalation effort zone performance")

            HStack(spacing: 16) {
                // Predominant Effort Zone
                OverviewMetricCard(
                    title: "Predominant Zone",
                    value: session.predominantEffortZone.rawValue,
                    subtitle: session.predominantEffortZone.description,
                    icon: "target",
                    color: session.predominantEffortZone.color
                )

                // Target Effort Zone Consistency
                OverviewMetricCard(
                    title: "Zone Consistency",
                    value: "\(Int(session.zoneConsistencyScore))%",
                    subtitle: "Target zone adherence",
                    icon: "scope",
                    color: session.zoneConsistencyScore >= 80 ? .green : session.zoneConsistencyScore >= 60 ? .orange : .red
                )
            }

            // Percentage in Target (Green) Effort Zone - clarified from header
            HStack(spacing: 16) {
                OverviewMetricCard(
                    title: "Time in Target Zone",
                    value: "\(Int(session.greenZonePercentage))%",
                    subtitle: "Optimal effort zone",
                    icon: "checkmark.circle",
                    color: session.greenZonePercentage >= 80 ? .green : session.greenZonePercentage >= 60 ? .orange : .red
                )

                // Spacer card to maintain layout
                Color.clear
                    .frame(maxWidth: .infinity)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
        )
    }

    // MARK: - D. Exhalation Time Compliance - Overview

    private var timeComplianceOverviewView: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Time Overview", subtitle: "Exhalation duration performance")

            HStack(spacing: 16) {
                // Average Exhalation Duration
                OverviewMetricCard(
                    title: "Average Duration",
                    value: "\(String(format: "%.1f", session.averageExhalationDuration))s",
                    subtitle: "Per exhalation",
                    icon: "clock.fill",
                    color: session.averageExhalationDuration >= 3.0 ? .green : .orange
                )

                // Optimal Duration Performance
                OverviewMetricCard(
                    title: "Optimal Duration",
                    value: "\(optimalDurationCount)/\(session.stepsCompleted)",
                    subtitle: "≥3.0s target met",
                    icon: "timer",
                    color: optimalDurationPercentage >= 80 ? .green : optimalDurationPercentage >= 60 ? .orange : .red
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
        )
    }

    private var feedbackView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Session Feedback")
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text(session.feedback)
                .font(.body)
                .foregroundColor(.white.opacity(0.8))
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.03))
                )
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
        )
    }
    
    // MARK: - Effort Content

    private var effortContent: some View {
        VStack(spacing: 20) {
            // Exhalation Effort Timeline Chart (primary chart for this tab)
            effortTimelineView
        }
    }
    
    private var effortTimelineView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Exhalation Effort Timeline")
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            // Effort zone timeline chart (primary chart for Effort tab)
            PressureTimelineChart(session: session)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
        )
    }
    

    
    // MARK: - Time Content

    private var timeContent: some View {
        VStack(spacing: 20) {
            // Exhalation Duration Compliance Chart (primary chart for this tab)
            durationComplianceView
        }
    }
    
    private var durationComplianceView: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Exhalation Duration Compliance Chart (primary chart for Time tab)
            ExhalationTimeChart(session: session)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
        )
    }

    // MARK: - Adherence Tab Views

    private var adherenceMetricsSummaryView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Adherence Metrics")
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                AdherenceMetricCard(
                    title: "Session Completion",
                    value: "\(session.stepsCompleted)/\(session.totalSteps)",
                    percentage: sessionCompletionPercentage,
                    icon: "checkmark.circle.fill",
                    color: sessionCompletionPercentage >= 100 ? .green : sessionCompletionPercentage >= 80 ? .orange : .red
                )

                AdherenceMetricCard(
                    title: "Quality Achievement",
                    value: session.quality.description,
                    percentage: qualityPercentage,
                    icon: "star.fill",
                    color: session.quality.color
                )

                AdherenceMetricCard(
                    title: "Duration Target",
                    value: session.formattedDuration,
                    percentage: durationTargetPercentage,
                    icon: "clock.fill",
                    color: durationTargetPercentage >= 100 ? .green : durationTargetPercentage >= 80 ? .orange : .red
                )

                AdherenceMetricCard(
                    title: "Effort Compliance",
                    value: "\(Int(session.greenZonePercentage))%",
                    percentage: session.greenZonePercentage,
                    icon: "target",
                    color: session.greenZonePercentage >= 80 ? .green : session.greenZonePercentage >= 60 ? .orange : .red
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
        )
    }



    // MARK: - Computed Properties
    
    private var averageBreathDuration: Double {
        // Calculate from session data - simplified for now
        return session.duration / Double(max(session.stepsCompleted, 1))
    }
    


    // MARK: - Overview Tab Computed Properties

    private var completionStatusText: String {
        if session.stepsCompleted >= session.totalSteps {
            return "Fully completed"
        } else {
            return "Partially completed"
        }
    }

    private var goodExhalationsCount: Int {
        // Count exhalations that meet both effort (green zone) and time (≥3.0s) criteria
        // This is a simplified calculation - in a real implementation, this would be tracked per exhalation
        let effortCompliant = Int(session.greenZonePercentage / 100.0 * Double(session.stepsCompleted))
        let timeCompliant = optimalDurationCount
        // Conservative estimate: minimum of both criteria
        return min(effortCompliant, timeCompliant)
    }

    private var goodExhalationsPercentage: Double {
        guard session.stepsCompleted > 0 else { return 0 }
        return Double(goodExhalationsCount) / Double(session.stepsCompleted) * 100
    }

    private var optimalDurationCount: Int {
        // Count exhalations with duration ≥3.0s
        // Simplified calculation based on average - in real implementation, track individual durations
        return session.averageExhalationDuration >= 3.0 ? session.stepsCompleted : Int(Double(session.stepsCompleted) * 0.7)
    }

    private var optimalDurationPercentage: Double {
        guard session.stepsCompleted > 0 else { return 0 }
        return Double(optimalDurationCount) / Double(session.stepsCompleted) * 100
    }

    // MARK: - Adherence Tab Computed Properties

    private var sessionCompletionPercentage: Double {
        guard session.totalSteps > 0 else { return 0 }
        return Double(session.stepsCompleted) / Double(session.totalSteps) * 100
    }

    private var qualityPercentage: Double {
        switch session.quality {
        case .excellent: return 100
        case .good: return 80
        case .fair: return 60
        case .poor: return 40
        case .none: return 0
        }
    }

    private var durationTargetPercentage: Double {
        // Assuming target session duration is based on total steps * 3 seconds minimum
        let targetDuration = Double(session.totalSteps) * 3.0
        guard targetDuration > 0 else { return 0 }
        return min(session.duration / targetDuration * 100, 100)
    }
}

// MARK: - Supporting Views

struct MetricCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(color)

            Text(value)
                .font(.system(size: 16, weight: .bold, design: .rounded))
                .foregroundColor(.white)

            Text(title)
                .font(.system(size: 11, weight: .medium, design: .rounded))
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct SummaryRow: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.cyan)
                .frame(width: 24)

            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)

            Spacer()

            Text(value)
                .font(.system(size: 14, weight: .semibold, design: .rounded))
                .foregroundColor(.white.opacity(0.7))
        }
        .padding(.vertical, 4)
    }
}

struct AnalysisRow: View {
    let title: String
    let value: String
    let target: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)

                Text("Target: \(target)")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
            }

            Spacer()

            HStack(spacing: 6) {
                Text(value)
                    .font(.system(size: 14, weight: .bold, design: .rounded))
                    .foregroundColor(color)

                Image(systemName: color == .green ? "checkmark.circle.fill" : "exclamationmark.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(color)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct EffortZoneCard: View {
    let title: String
    var zone: ExhalationEffortZone?
    var value: String?
    var color: Color?
    let subtitle: String

    init(title: String, zone: ExhalationEffortZone, subtitle: String) {
        self.title = title
        self.zone = zone
        self.value = zone.rawValue
        self.color = zone.color
        self.subtitle = subtitle
    }

    init(title: String, value: String, color: Color, subtitle: String) {
        self.title = title
        self.zone = nil
        self.value = value
        self.color = color
        self.subtitle = subtitle
    }

    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)

            Text(value ?? "N/A")
                .font(.system(size: 16, weight: .bold, design: .rounded))
                .foregroundColor(color ?? .white)
                .multilineTextAlignment(.center)

            Text(subtitle)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity, minHeight: 80)
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill((color ?? .white).opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke((color ?? .white).opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Overview Tab Supporting Views

struct SectionHeader: View {
    let title: String
    let subtitle: String

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            Text(subtitle)
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.7))
        }
    }
}

struct OverviewMetricCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 12) {
            // Icon
            Image(systemName: icon)
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(color)

            // Value
            Text(value)
                .font(.system(size: 18, weight: .bold, design: .rounded))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)

            // Title and subtitle
            VStack(spacing: 2) {
                Text(title)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)

                Text(subtitle)
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
        }
        .frame(maxWidth: .infinity, minHeight: 120)
        .padding(.vertical, 16)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Adherence Tab Supporting Views

struct AdherenceMetricCard: View {
    let title: String
    let value: String
    let percentage: Double
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 12) {
            // Icon with circular progress background
            ZStack {
                Circle()
                    .stroke(color.opacity(0.2), lineWidth: 4)
                    .frame(width: 50, height: 50)

                Circle()
                    .trim(from: 0, to: percentage / 100)
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .frame(width: 50, height: 50)
                    .rotationEffect(.degrees(-90))

                Image(systemName: icon)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(color)
            }

            // Value and percentage
            VStack(spacing: 4) {
                Text(value)
                    .font(.system(size: 16, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)

                Text("\(Int(percentage))%")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(color)
            }

            // Title
            Text(title)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity, minHeight: 140)
        .padding(.vertical, 16)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}
