# 🫁 Breath2 iOS App - Complete Code Visualization

## 📋 Table of Contents
1. [Application Overview](#application-overview)
2. [Architecture Diagram](#architecture-diagram)
3. [Core Components](#core-components)
4. [Data Flow](#data-flow)
5. [Hard-coded Values & Configuration](#hard-coded-values--configuration)
6. [Key Algorithms](#key-algorithms)
7. [User Interface Components](#user-interface-components)
8. [State Management](#state-management)
9. [Performance Considerations](#performance-considerations)
10. [Module Dependencies](#module-dependencies)

---

## 🎯 Application Overview

**Breath2** is a sophisticated iOS application that provides **Positive Expiratory Pressure (PEP) therapy** for respiratory conditions. The app uses advanced pitch detection algorithms to convert audio input into pressure readings, enabling patients to perform breathing exercises with real-time feedback.

### Key Features
- **Real-time Audio Analysis**: Converts breathing sounds to pressure measurements
- **Therapy Sessions**: Guided breathing exercises with multiple difficulty levels
- **Progress Tracking**: Session history and adherence monitoring
- **Configurable Algorithms**: Multiple presets for different user needs
- **Professional UI**: Medical-grade interface with dark theme

---

## 🏗️ Architecture Diagram

```mermaid
graph TD
    A[Breath2App] --> B[ContentView]
    B --> C[TabBarView]
    
    C --> D[TherapyView]
    C --> E[HistoryView]
    C --> F[SettingsView]
    C --> G[ProfileView]
    
    D --> H[AudioManager]
    D --> I[TherapySession]
    D --> J[TherapyConfiguration]
    
    H --> K[PitchDetector]
    H --> L[BreathDetector]
    H --> M[PressureCalculator]
    
    K --> N[AlgorithmConfiguration]
    H --> O[ConfigurationManager]
    
    I --> P[SessionHistoryManager]
    I --> Q[BreathPhaseManager]
    
    E --> P
    F --> O
    F --> J
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style H fill:#e8f5e8
    style K fill:#fff3e0
    style I fill:#fce4ec
```

---

## 🔧 Core Components

### 1. **AudioManager** - The Heart of the System
**File:** `AudioManager.swift`
**Role:** Central audio processing coordinator

#### Key Functions:
- `startRecording()` - Initializes audio session and begins processing
- `stopRecording()` - Cleans up audio resources
- `processAudioBuffer(AVAudioPCMBuffer)` - Main audio processing pipeline
- `updateConfiguration(AlgorithmConfiguration)` - Runtime configuration updates

#### Data Flow:
```
Microphone → AVAudioEngine → AudioBuffer → PitchDetector → PressureCalculator → UI Updates
```

#### Hard-coded Values:
- **Sample Rate:** 44,100 Hz (standard audio sampling)
- **Buffer Size:** 0.1 seconds (4,410 samples)
- **Audio Category:** `.playAndRecord` with `.measurement` mode

---

### 2. **PitchDetector** - Core Algorithm Engine
**File:** `PitchDetector.swift`
**Role:** Implements the Acapella paper's pitch detection algorithm

#### Key Functions:
- `processChunk([Float])` - Main processing pipeline
- `subtractSmoothedVersion([Float])` - Step 1: High-pass filtering
- `computeSquare([Float])` - Step 2: Signal squaring
- `downsample([Float])` - Step 3: Downsampling
- `applyGaussianSmoothing([Float])` - Step 4: Gaussian smoothing
- `computeAutocorrelation([Float])` - Step 5: Pitch detection

#### Hard-coded Values:
- **Min Frequency:** 7 Hz (extended from paper's 10 Hz)
- **Max Frequency:** 40 Hz (paper specification)
- **Correlation Threshold:** 0.6 (paper specification)
- **Downsample Factor:** 45 (44.1kHz → 980Hz)
- **Target Buffer Length:** 300 samples

---

### 3. **PressureCalculator** - Medical Model Implementation
**File:** `PressureCalculator.swift`
**Role:** Converts pitch to pressure using linear regression model

#### Key Functions:
- `calculatePressure(fromPitch: Float) -> Float` - Main conversion
- `isValidFrequency(Float) -> Bool` - Input validation
- `getPressureFeedback(Float) -> String` - User guidance

#### Hard-coded Values (⚠️ **MEDICAL CRITICAL**):
- **Slope:** 1.119 cm H2O per Hz (from research data)
- **Intercept:** -4.659 cm H2O (from research data)
- **R² Value:** 0.886 (correlation strength)
- **Data Points:** 9,993 (research sample size)
- **Target Range:** 10-20 cm H2O (therapeutic range)

---

### 4. **BreathDetector** - Intelligent Breath Recognition
**File:** `BreathDetector.swift`
**Role:** Detects individual breaths from audio and pressure signals

#### Key Functions:
- `processBreathingSignals(pressure, frequency, audioLevel)` - Main detection
- `updateBreathState(hasOscillation, timestamp)` - State machine
- `processCompletedBreath(BreathEvent)` - Breath completion handling

#### Hard-coded Values:
- **Oscillation Threshold:** 0.005 (minimum audio level)
- **Minimum Pressure:** 3.0 cm H2O (breath signal threshold)
- **Minimum Frequency:** 5.0 Hz (breath signal threshold)
- **Minimum Duration:** 1.5 seconds (valid breath)
- **Max Silence Gap:** 0.3 seconds (breath continuation)

---

### 5. **TherapySession** - Session Management
**File:** `TherapySession.swift`
**Role:** Manages therapy sessions, step progression, and performance tracking

#### Key Functions:
- `startSession()` - Initialize new therapy session
- `processCompletedBreath(BreathEvent)` - Handle completed breaths
- `calculateBreathPerformance()` - Evaluate breath quality
- `updatePressure(Double)` - Real-time pressure monitoring

#### Hard-coded Values:
- **Target Pressure:** 15.0 cm H2O (default therapy target)
- **Green Zone:** 10.0-20.0 cm H2O (optimal pressure range)
- **Red Zone:** >20.0 cm H2O (excessive pressure)
- **Amber Zone:** <10.0 cm H2O (insufficient pressure)

---

## 🔄 Data Flow

### Primary Data Pipeline:
```mermaid
sequenceDiagram
    participant M as Microphone
    participant AM as AudioManager
    participant PD as PitchDetector
    participant PC as PressureCalculator
    participant BD as BreathDetector
    participant TS as TherapySession
    participant UI as User Interface
    
    M->>AM: Raw Audio Buffer
    AM->>PD: Float Array [4410 samples]
    PD->>PD: 5-Step Processing Pipeline
    PD->>AM: Detected Frequency (Hz)
    AM->>PC: Frequency for Conversion
    PC->>AM: Pressure (cm H2O)
    AM->>BD: Pressure + Frequency + Audio Level
    BD->>TS: Completed Breath Event
    TS->>UI: Session State Updates
    
    Note over PD: Steps: Filter → Square → Downsample → Smooth → Autocorrelation
    Note over PC: Linear Model: P = -4.659 + 1.119 × F
    Note over BD: State Machine: Idle → Starting → Active → Ending → Completed
```

### Configuration Flow:
```mermaid
graph LR
    A[User Settings] --> B[ConfigurationManager]
    B --> C[AlgorithmConfiguration]
    C --> D[PitchDetector]
    C --> E[AudioManager]
    C --> F[PressureCalculator]
    
    B --> G[UserDefaults]
    G --> B
    
    style C fill:#fff3e0
    style B fill:#e8f5e8
```

---

## ⚙️ Hard-coded Values & Configuration

### Audio Processing Constants:
```swift
// AudioManager.swift
private var sampleRate: Double = 44100.0
private var bufferSize: AVAudioFrameCount = 4410 // 0.1 seconds
```

### Pitch Detection Parameters:
```swift
// PitchDetector.swift
private var minFreq: Int = 7              // Hz
private var maxFreq: Int = 40             // Hz
private var correlationThreshold: Float = 0.6
private var downsampleFactor: Int = 45    // 44.1kHz → 980Hz
private var targetBufferLength: Int = 300  // samples
```

### Medical Model Constants (⚠️ **DO NOT MODIFY**):
```swift
// PressureCalculator.swift
private let slope: Float = 1.119        // cm H2O per Hz
private let intercept: Float = -4.659   // cm H2O
private let minValidFreq: Float = 7.0   // Hz
private let maxValidFreq: Float = 40.0  // Hz
private let minValidPressure: Float = 6.0   // cm H2O
private let maxValidPressure: Float = 30.0  // cm H2O
```

### Breath Detection Thresholds:
```swift
// BreathDetector.swift
private let oscillationThreshold: Float = 0.005
private let minimumPressureThreshold: Float = 3.0    // cm H2O
private let minimumFrequencyThreshold: Float = 5.0   // Hz
private let minimumBreathDuration: TimeInterval = 1.5 // seconds
private let maxSilenceGap: TimeInterval = 0.3        // seconds
```

### Configuration Presets:
The app includes 11 predefined configurations:

1. **Paper Compliant** - Strict research specifications
2. **Standard** - Default balanced settings
3. **Real-Time Feedback** - Low latency (50ms)
4. **High Accuracy** - Precision mode with higher latency
5. **Stable Readings** - Smooth output, slower response
6. **Low Power** - Reduced CPU usage (22.05kHz)
7. **Noisy Environment** - Better noise rejection
8. **Pediatric** - Optimized for children (5-30 Hz)
9. **Elderly/COPD** - Stability focus
10. **Athletic** - High-performance mode
11. **Research** - Clinical study mode (48kHz, data logging)

---

## 🧮 Key Algorithms

### 1. **5-Step Pitch Detection Pipeline**
Based on the Acapella research paper:

```swift
func processChunk(_ audioData: [Float]) -> Float {
    // Step 1: High-pass filtering (remove DC component)
    let filteredAudio = subtractSmoothedVersion(audioData)
    
    // Step 2: Square the signal (emphasize periodic components)
    let squaredAudio = computeSquare(filteredAudio)
    
    // Step 3: Downsample by averaging (reduce computation)
    let downsampledAudio = downsample(squaredAudio)
    
    // Step 4: Gaussian smoothing (reduce noise)
    let smoothedAudio = applyGaussianSmoothing(downsampledAudio)
    
    // Step 5: Autocorrelation (find periodicity)
    let detectedPitch = computeAutocorrelation(smoothedAudio)
    
    return detectedPitch
}
```

### 2. **Two-Stage Autocorrelation**
Optimized search algorithm:

```swift
// Stage 1: Coarse search (step size = 3)
for lag in stride(from: minPeriod, through: maxPeriod, by: 3) {
    let correlation = computeCorrelationAtLag(audioData, lag: lag)
    // Find approximate peak
}

// Stage 2: Fine search (±5 samples around peak)
for lag in (coarsePeak - 5)...(coarsePeak + 5) {
    let correlation = computeCorrelationAtLag(audioData, lag: lag)
    // Find exact peak
}
```

### 3. **Economical Autocorrelation**
Intelligent search based on previous detections:

```swift
if lastDetectedPitch > 0 {
    // Search in ±20% window around last detection
    let expectedLag = lastWavelength
    let searchWindow = Int(0.2 * Float(expectedLag))
    // Only search narrow window first
}
```

### 4. **Breath State Machine**
Intelligent breath detection:

```swift
enum BreathState {
    case idle       // No breathing detected
    case starting   // Oscillations detected, building
    case active     // Valid breath in progress (>1.5s)
    case ending     // Oscillations stopped, validating
    case completed  // Valid breath completed
}
```

---

## 🎨 User Interface Components

### Main Navigation:
- **TabBarView** - 4 tabs: History, PEP Therapy, Settings, Profile
- **Professional dark theme** with cyan accents
- **Custom tab bar appearance** with gradient backgrounds

### Therapy Interface:
- **Real-time pressure gauge** with color-coded zones
- **Breath counter** with step progression
- **Audio level indicator** for feedback
- **Session timer** and progress tracking

### Settings Interface:
- **Configuration presets** with performance metrics
- **Parameter adjustment** sliders and controls
- **Export/import** functionality for configurations
- **Developer tools** (DEBUG builds only)

### History Interface:
- **Session timeline** with collapsible date groups
- **Adherence tracking** with percentage scores
- **Quality indicators** (Green/Amber/Red performance)
- **Compliance trend charts** showing progress

---

## 📊 State Management

### ObservableObject Classes:
1. **AudioManager** - Audio processing state
2. **TherapySession** - Session progress and metrics
3. **TherapyConfiguration** - Daily therapy settings
4. **ConfigurationManager** - Algorithm parameters
5. **SessionHistoryManager** - Historical data

### Published Properties:
```swift
// AudioManager
@Published var currentFrequency: Double = 0.0
@Published var currentPressure: Double = 0.0
@Published var audioLevel: Float = 0.0
@Published var breathDetector = BreathDetector()

// TherapySession
@Published var currentStep: Int = 1
@Published var totalSteps: Int = 10
@Published var sessionProgress: Double = 0.0
@Published var isActive: Bool = false
@Published var feedback: String = "Ready to begin"
```

---

## ⚡ Performance Considerations

### Optimization Strategies:
1. **vDSP Framework** - Vectorized math operations
2. **Accelerate Framework** - High-performance computing
3. **Buffer Management** - Sliding window approach
4. **Memory Pooling** - Reusable arrays for processing
5. **Background Processing** - Audio processing on separate thread

### Performance Metrics:
- **Processing Time:** <10ms per buffer (target)
- **Memory Usage:** <50MB total
- **CPU Usage:** <30% sustained
- **Detection Rate:** >80% successful detections
- **Latency:** 50-150ms (configurable)

### Critical Paths:
```swift
// Hot path - called every 100ms
func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
    let audioData = extractFloatArray(buffer)      // ~1ms
    let pitch = pitchDetector.processChunk(audioData)  // ~8ms
    let pressure = pressureCalculator.calculatePressure(fromPitch: pitch)  // ~0.1ms
    // Total: ~9ms per buffer
}
```

---

## 🔗 Module Dependencies

### External Frameworks:
- **Foundation** - Core Swift functionality
- **SwiftUI** - User interface framework
- **AVFoundation** - Audio processing
- **Accelerate** - Mathematical operations
- **Charts** - Data visualization
- **Combine** - Reactive programming

### Internal Dependencies:
```mermaid
graph TD
    A[Breath2App] --> B[Foundation]
    C[AudioManager] --> D[AVFoundation]
    C --> E[Accelerate]
    F[PitchDetector] --> E
    F --> G[AlgorithmConfiguration]
    H[TherapyView] --> I[SwiftUI]
    H --> J[Charts]
    K[ConfigurationManager] --> L[Combine]
    
    style B fill:#e3f2fd
    style D fill:#e8f5e8
    style E fill:#fff3e0
    style I fill:#f3e5f5
    style J fill:#fce4ec
```

### Component Relationships:
- **AudioManager** manages **PitchDetector**, **BreathDetector**, **PressureCalculator**
- **TherapySession** uses **AudioManager** and **TherapyConfiguration**
- **ConfigurationManager** provides settings to **AudioManager**
- **SessionHistoryManager** stores data from **TherapySession**
- **All UI components** observe state through **@ObservedObject** and **@EnvironmentObject**

---

## 🔍 Key Insights

### Design Patterns:
1. **MVVM Architecture** - Clean separation of concerns
2. **Observer Pattern** - Reactive UI updates
3. **State Machine** - Breath detection logic
4. **Strategy Pattern** - Multiple configuration presets
5. **Factory Pattern** - Configuration creation

### Medical Compliance:
- **FDA Guidelines** - Follows medical device development practices
- **Research-Based** - Algorithm based on peer-reviewed paper
- **Validation** - Extensive parameter validation
- **Safety** - Pressure limits and user feedback

### Code Quality:
- **Comprehensive Documentation** - Every function documented
- **Error Handling** - Robust error management
- **Performance Monitoring** - Built-in metrics tracking
- **Testing** - Unit tests for core algorithms
- **Modularity** - Clean component separation

---

## 🎯 Summary

The **Breath2** application is a sophisticated medical device software that combines:

- **Advanced Signal Processing** (5-step pitch detection pipeline)
- **Medical-Grade Accuracy** (Linear regression model with r² = 0.886)
- **Intelligent Breath Detection** (State machine with validation)
- **Professional UI** (SwiftUI with custom theming)
- **Comprehensive Configuration** (11 preset modes)
- **Performance Optimization** (vDSP + Accelerate frameworks)

The codebase demonstrates excellent software engineering practices with clear separation of concerns, comprehensive documentation, and robust error handling. The medical algorithm implementation strictly follows research specifications while providing flexibility for different use cases.

**Total Components:** 33 Swift files, 5 core processing classes, 4 UI sections, 50+ configurable parameters, and 11 preset configurations.

---

*Generated on: $(date)*
*Codebase Analysis: Complete ✅*
